using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using System.Diagnostics;

namespace SmaTrendFollower.Console.Scripts;

/// <summary>
/// Comprehensive system validation script for enhanced trading system
/// Runs all validation checks and provides detailed report
/// </summary>
public static class ValidateEnhancedSystem
{
    /// <summary>
    /// Main validation entry point
    /// </summary>
    public static async Task<bool> RunValidationAsync(IServiceProvider serviceProvider)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        
        logger.LogInformation("🚀 Starting Enhanced Trading System Validation");
        logger.LogInformation("=" + new string('=', 60));

        var validationResults = new List<ValidationResult>();
        var overallStopwatch = Stopwatch.StartNew();

        try
        {
            // Run all validation checks
            validationResults.Add(await ValidateServiceRegistrationAsync(serviceProvider));
            validationResults.Add(await ValidateConfigurationAsync(serviceProvider));
            validationResults.Add(await ValidateMigrationServiceAsync(serviceProvider));
            validationResults.Add(await ValidateDataRetrievalAsync(serviceProvider));
            validationResults.Add(await ValidateRateLimitingAsync(serviceProvider));
            validationResults.Add(await ValidateStalenessServiceAsync(serviceProvider));
            validationResults.Add(await ValidateSignalGenerationAsync(serviceProvider));
            validationResults.Add(await ValidateSystemIntegrationAsync(serviceProvider));

            overallStopwatch.Stop();

            // Generate final report
            GenerateFinalReport(logger, validationResults, overallStopwatch.Elapsed);

            var allPassed = validationResults.All(r => r.Success);
            
            if (allPassed)
            {
                logger.LogInformation("🎉 ALL VALIDATIONS PASSED - Enhanced Trading System is ready for production!");
            }
            else
            {
                logger.LogError("❌ Some validations failed - Review the issues above before deploying");
            }

            return allPassed;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "💥 Critical error during system validation");
            return false;
        }
    }

    private static async Task<ValidationResult> ValidateServiceRegistrationAsync(IServiceProvider serviceProvider)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("🔍 Validating Service Registration...");

            var requiredServices = new Dictionary<Type, string>
            {
                { typeof(ISignalGenerator), "Signal Generator" },
                { typeof(IMarketDataService), "Market Data Service" },
                { typeof(IEnhancedDataRetrievalService), "Enhanced Data Retrieval" },
                { typeof(IAdaptiveRateLimitingService), "Adaptive Rate Limiting" },
                { typeof(IFlexibleDataStalenessService), "Flexible Staleness Service" },
                { typeof(IEnhancedServicesMigrationService), "Migration Service" }
            };

            var missingServices = new List<string>();

            foreach (var (serviceType, serviceName) in requiredServices)
            {
                var service = serviceProvider.GetService(serviceType);
                if (service == null)
                {
                    missingServices.Add(serviceName);
                    logger.LogError("❌ Missing service: {ServiceName}", serviceName);
                }
                else
                {
                    logger.LogDebug("✅ Found service: {ServiceName}", serviceName);
                }
            }

            stopwatch.Stop();

            if (missingServices.Any())
            {
                return new ValidationResult
                {
                    TestName = "Service Registration",
                    Success = false,
                    Duration = stopwatch.Elapsed,
                    ErrorMessage = $"Missing services: {string.Join(", ", missingServices)}"
                };
            }

            logger.LogInformation("✅ Service Registration validation passed ({Duration}ms)", stopwatch.ElapsedMilliseconds);
            return new ValidationResult
            {
                TestName = "Service Registration",
                Success = true,
                Duration = stopwatch.Elapsed,
                Details = $"All {requiredServices.Count} required services registered successfully"
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Service Registration validation failed");
            return new ValidationResult
            {
                TestName = "Service Registration",
                Success = false,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message
            };
        }
    }

    private static async Task<ValidationResult> ValidateConfigurationAsync(IServiceProvider serviceProvider)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("⚙️ Validating Configuration...");

            var enhancedOptions = serviceProvider.GetService<EnhancedServicesOptions>();
            if (enhancedOptions == null)
            {
                throw new InvalidOperationException("EnhancedServicesOptions not configured");
            }

            logger.LogInformation("📋 Enhanced Services Config:");
            logger.LogInformation("  - Enhanced Data Retrieval: {Enabled}", enhancedOptions.EnableEnhancedDataRetrieval);
            logger.LogInformation("  - Adaptive Rate Limiting: {Enabled}", enhancedOptions.EnableAdaptiveRateLimit);
            logger.LogInformation("  - Adaptive Signal Generation: {Enabled}", enhancedOptions.EnableAdaptiveSignalGeneration);
            logger.LogInformation("  - Synthetic Data: {Enabled}", enhancedOptions.EnableSyntheticData);
            logger.LogInformation("  - Emergency Mode: {Enabled}", enhancedOptions.EnableEmergencyMode);

            stopwatch.Stop();
            logger.LogInformation("✅ Configuration validation passed ({Duration}ms)", stopwatch.ElapsedMilliseconds);

            return new ValidationResult
            {
                TestName = "Configuration",
                Success = true,
                Duration = stopwatch.Elapsed,
                Details = "All configuration sections loaded successfully"
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Configuration validation failed");
            return new ValidationResult
            {
                TestName = "Configuration",
                Success = false,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message
            };
        }
    }

    private static async Task<ValidationResult> ValidateMigrationServiceAsync(IServiceProvider serviceProvider)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("🔄 Validating Migration Service...");

            var migrationService = serviceProvider.GetRequiredService<IEnhancedServicesMigrationService>();

            // Test health check
            var healthResult = await migrationService.PerformHealthCheckAsync();
            logger.LogInformation("🏥 Health Check: {Status}", healthResult.OverallHealth);

            // Test migration report
            var report = migrationService.GetMigrationReport();
            logger.LogInformation("📊 Migration Status: {Status}", report.OverallStatus);

            // Test feature flags
            var flags = new
            {
                EnhancedData = migrationService.ShouldUseEnhancedDataRetrieval(),
                AdaptiveRate = migrationService.ShouldUseAdaptiveRateLimit(),
                AdaptiveSignal = migrationService.ShouldUseAdaptiveSignalGeneration()
            };

            logger.LogInformation("🎛️ Feature Flags: Data={Data}, Rate={Rate}, Signal={Signal}",
                flags.EnhancedData, flags.AdaptiveRate, flags.AdaptiveSignal);

            stopwatch.Stop();
            logger.LogInformation("✅ Migration Service validation passed ({Duration}ms)", stopwatch.ElapsedMilliseconds);

            return new ValidationResult
            {
                TestName = "Migration Service",
                Success = true,
                Duration = stopwatch.Elapsed,
                Details = $"Health: {healthResult.OverallHealth}, Status: {report.OverallStatus}"
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Migration Service validation failed");
            return new ValidationResult
            {
                TestName = "Migration Service",
                Success = false,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message
            };
        }
    }

    private static async Task<ValidationResult> ValidateDataRetrievalAsync(IServiceProvider serviceProvider)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("📊 Validating Enhanced Data Retrieval...");

            var enhancedDataService = serviceProvider.GetService<IEnhancedDataRetrievalService>();
            if (enhancedDataService == null)
            {
                logger.LogWarning("⚠️ Enhanced data retrieval service not available");
                return new ValidationResult
                {
                    TestName = "Data Retrieval",
                    Success = true,
                    Duration = stopwatch.Elapsed,
                    Details = "Service not available (feature disabled)"
                };
            }

            // Test basic functionality (this would normally use real data)
            logger.LogInformation("📈 Enhanced data retrieval service is available and configured");

            stopwatch.Stop();
            logger.LogInformation("✅ Data Retrieval validation passed ({Duration}ms)", stopwatch.ElapsedMilliseconds);

            return new ValidationResult
            {
                TestName = "Data Retrieval",
                Success = true,
                Duration = stopwatch.Elapsed,
                Details = "Enhanced data retrieval service available"
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Data Retrieval validation failed");
            return new ValidationResult
            {
                TestName = "Data Retrieval",
                Success = false,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message
            };
        }
    }

    private static async Task<ValidationResult> ValidateRateLimitingAsync(IServiceProvider serviceProvider)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("🚦 Validating Rate Limiting...");

            var rateLimitService = serviceProvider.GetService<IAdaptiveRateLimitingService>();
            if (rateLimitService == null)
            {
                logger.LogWarning("⚠️ Rate limiting service not available");
                return new ValidationResult
                {
                    TestName = "Rate Limiting",
                    Success = true,
                    Duration = stopwatch.Elapsed,
                    Details = "Service not available (feature disabled)"
                };
            }

            // Test basic rate limiting functionality
            var result = await rateLimitService.TryAcquireAsync("ValidationTest", "test_operation");
            if (result.IsSuccess)
            {
                rateLimitService.Release("ValidationTest", "test_operation", true, TimeSpan.FromMilliseconds(10));
            }

            var stats = rateLimitService.GetStats("ValidationTest");
            logger.LogInformation("📊 Rate Limit Stats: Limit={Limit}, Available={Available}, Total={Total}",
                stats.CurrentLimit, stats.AvailablePermits, stats.TotalRequests);

            stopwatch.Stop();
            logger.LogInformation("✅ Rate Limiting validation passed ({Duration}ms)", stopwatch.ElapsedMilliseconds);

            return new ValidationResult
            {
                TestName = "Rate Limiting",
                Success = true,
                Duration = stopwatch.Elapsed,
                Details = $"Rate limiting functional, current limit: {stats.CurrentLimit}"
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Rate Limiting validation failed");
            return new ValidationResult
            {
                TestName = "Rate Limiting",
                Success = false,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message
            };
        }
    }

    private static async Task<ValidationResult> ValidateStalenessServiceAsync(IServiceProvider serviceProvider)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("⏰ Validating Staleness Service...");

            var stalenessService = serviceProvider.GetService<IFlexibleDataStalenessService>();
            if (stalenessService == null)
            {
                logger.LogWarning("⚠️ Staleness service not available");
                return new ValidationResult
                {
                    TestName = "Staleness Service",
                    Success = true,
                    Duration = stopwatch.Elapsed,
                    Details = "Service not available (feature disabled)"
                };
            }

            var status = stalenessService.GetCurrentStatus();
            logger.LogInformation("📊 Staleness Status: Policy={Policy}, Emergency={Emergency}",
                status.CurrentPolicy, status.EmergencyModeActive);

            // Test data validation
            var testResult = await stalenessService.ValidateDataFreshnessAsync(
                DateTime.UtcNow.AddMinutes(-10), DataType.HistoricalBars, "ValidationTest");

            logger.LogInformation("🔍 Test Validation: Level={Level}, Acceptable={Acceptable}, Quality={Quality:F2}",
                testResult.StalenessLevel, testResult.IsAcceptable, testResult.QualityScore);

            stopwatch.Stop();
            logger.LogInformation("✅ Staleness Service validation passed ({Duration}ms)", stopwatch.ElapsedMilliseconds);

            return new ValidationResult
            {
                TestName = "Staleness Service",
                Success = true,
                Duration = stopwatch.Elapsed,
                Details = $"Policy: {status.CurrentPolicy}, Emergency: {status.EmergencyModeActive}"
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Staleness Service validation failed");
            return new ValidationResult
            {
                TestName = "Staleness Service",
                Success = false,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message
            };
        }
    }

    private static async Task<ValidationResult> ValidateSignalGenerationAsync(IServiceProvider serviceProvider)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("📡 Validating Signal Generation...");

            var signalGenerator = serviceProvider.GetRequiredService<ISignalGenerator>();
            var enhancedSignalGenerator = serviceProvider.GetService<IEnhancedSignalGenerator>();

            logger.LogInformation("🔧 Signal Generator: {Type}", signalGenerator.GetType().Name);
            
            if (enhancedSignalGenerator != null)
            {
                logger.LogInformation("✨ Enhanced Signal Generator: Available (decorator pattern active)");
                
                var errorStats = enhancedSignalGenerator.GetErrorStatistics();
                logger.LogInformation("📊 Error Stats: Tracked={Tracked}, Errors={Errors}",
                    errorStats.TotalSymbolsTracked, errorStats.TotalErrors);
            }

            stopwatch.Stop();
            logger.LogInformation("✅ Signal Generation validation passed ({Duration}ms)", stopwatch.ElapsedMilliseconds);

            return new ValidationResult
            {
                TestName = "Signal Generation",
                Success = true,
                Duration = stopwatch.Elapsed,
                Details = enhancedSignalGenerator != null ? "Enhanced generator active" : "Standard generator only"
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Signal Generation validation failed");
            return new ValidationResult
            {
                TestName = "Signal Generation",
                Success = false,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message
            };
        }
    }

    private static async Task<ValidationResult> ValidateSystemIntegrationAsync(IServiceProvider serviceProvider)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            logger.LogInformation("🔗 Validating System Integration...");

            // Test that all services can work together without conflicts
            var migrationService = serviceProvider.GetRequiredService<IEnhancedServicesMigrationService>();
            var healthResult = await migrationService.PerformHealthCheckAsync();

            if (healthResult.OverallHealth == HealthStatus.Unhealthy)
            {
                throw new InvalidOperationException($"System health check failed: {healthResult.ErrorMessage}");
            }

            logger.LogInformation("🏥 System Health: {Health}", healthResult.OverallHealth);

            stopwatch.Stop();
            logger.LogInformation("✅ System Integration validation passed ({Duration}ms)", stopwatch.ElapsedMilliseconds);

            return new ValidationResult
            {
                TestName = "System Integration",
                Success = true,
                Duration = stopwatch.Elapsed,
                Details = $"Overall health: {healthResult.OverallHealth}"
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ System Integration validation failed");
            return new ValidationResult
            {
                TestName = "System Integration",
                Success = false,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message
            };
        }
    }

    private static void GenerateFinalReport(ILogger logger, List<ValidationResult> results, TimeSpan totalDuration)
    {
        logger.LogInformation("");
        logger.LogInformation("📋 VALIDATION SUMMARY");
        logger.LogInformation("=" + new string('=', 60));

        var passedCount = results.Count(r => r.Success);
        var failedCount = results.Count(r => !r.Success);

        foreach (var result in results)
        {
            var status = result.Success ? "✅ PASS" : "❌ FAIL";
            var details = result.Success ? result.Details : result.ErrorMessage;
            
            logger.LogInformation("{Status} {TestName} ({Duration}ms) - {Details}",
                status, result.TestName, result.Duration.TotalMilliseconds, details);
        }

        logger.LogInformation("");
        logger.LogInformation("📊 RESULTS: {Passed}/{Total} tests passed in {Duration:F1}s",
            passedCount, results.Count, totalDuration.TotalSeconds);

        if (failedCount > 0)
        {
            logger.LogInformation("⚠️  {Failed} test(s) failed - review issues above", failedCount);
        }
    }

    private sealed class ValidationResult
    {
        public string TestName { get; set; } = string.Empty;
        public bool Success { get; set; }
        public TimeSpan Duration { get; set; }
        public string? Details { get; set; }
        public string? ErrorMessage { get; set; }
    }
}

using System;
using System.Collections.Generic;
using System.Linq;

namespace SmaTrendFollower.MarketSessionValidation;

/// <summary>
/// Standalone validator for Monday market session logic using historical data patterns
/// This can be run independently to validate market session logic without full project compilation
/// </summary>
public class MondayMarketSessionValidator
{
    /// <summary>
    /// Validates Monday market session logic against known patterns and historical data
    /// </summary>
    public static ValidationReport ValidateMondayMarketSessions()
    {
        Console.WriteLine("🚀 Starting Monday Market Session Validation");
        Console.WriteLine($"Validation started at: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
        
        var report = new ValidationReport();
        
        // Test 1: Known Monday holidays
        Console.WriteLine("\n📅 Test 1: Known Monday Holidays");
        ValidateKnownMondayHolidays(report);
        
        // Test 2: Regular Monday market hours
        Console.WriteLine("\n⏰ Test 2: Regular Monday Market Hours");
        ValidateRegularMondayHours(report);
        
        // Test 3: Monday edge cases
        Console.WriteLine("\n🔍 Test 3: Monday Edge Cases");
        ValidateMondayEdgeCases(report);
        
        // Test 4: DST transitions
        Console.WriteLine("\n🌅 Test 4: DST Transitions");
        ValidateDSTTransitions(report);
        
        // Generate final report
        GenerateFinalReport(report);
        
        return report;
    }
    
    private static void ValidateKnownMondayHolidays(ValidationReport report)
    {
        // Known Monday holidays for 2024
        var mondayHolidays = new[]
        {
            new DateTime(2024, 1, 1),   // New Year's Day
            new DateTime(2024, 1, 15),  // MLK Day (3rd Monday in January)
            new DateTime(2024, 2, 19),  // Presidents Day (3rd Monday in February)
            new DateTime(2024, 5, 27),  // Memorial Day (Last Monday in May)
            new DateTime(2024, 9, 2),   // Labor Day (1st Monday in September)
        };
        
        foreach (var holiday in mondayHolidays)
        {
            var testTime = holiday.Date.AddHours(19); // 2:00 PM ET = 7:00 PM UTC (EST)
            var canTrade = SimulateMarketSessionLogic(testTime);
            var expected = false; // Holidays should not allow trading
            
            var result = new TestResult
            {
                TestName = $"Monday Holiday: {holiday:yyyy-MM-dd}",
                TestTime = testTime,
                Expected = expected,
                Actual = canTrade,
                IsValid = expected == canTrade,
                Description = $"Holiday validation for {holiday:yyyy-MM-dd}"
            };
            
            report.Results.Add(result);
            Console.WriteLine($"  {holiday:yyyy-MM-dd}: {(result.IsValid ? "✅" : "❌")} Expected={expected}, Got={canTrade}");
        }
    }
    
    private static void ValidateRegularMondayHours(ValidationReport report)
    {
        var regularMonday = new DateTime(2024, 3, 4); // Regular Monday
        
        var testCases = new[]
        {
            (regularMonday.AddHours(8), false, "8:00 AM ET - Pre-market"),
            (regularMonday.AddHours(9).AddMinutes(29), false, "9:29 AM ET - Just before open"),
            (regularMonday.AddHours(9).AddMinutes(30), true, "9:30 AM ET - Market open"),
            (regularMonday.AddHours(12), true, "12:00 PM ET - Mid-day"),
            (regularMonday.AddHours(16), true, "4:00 PM ET - Market close"),
            (regularMonday.AddHours(16).AddMinutes(1), false, "4:01 PM ET - After close"),
            (regularMonday.AddHours(20), false, "8:00 PM ET - Evening"),
        };
        
        foreach (var (testTime, expected, description) in testCases)
        {
            var canTrade = SimulateMarketSessionLogic(testTime);
            
            var result = new TestResult
            {
                TestName = "Regular Monday Hours",
                TestTime = testTime,
                Expected = expected,
                Actual = canTrade,
                IsValid = expected == canTrade,
                Description = description
            };
            
            report.Results.Add(result);
            Console.WriteLine($"  {description}: {(result.IsValid ? "✅" : "❌")} Expected={expected}, Got={canTrade}");
        }
    }
    
    private static void ValidateMondayEdgeCases(ValidationReport report)
    {
        // Test Monday after a long weekend
        var mondayAfterLongWeekend = new DateTime(2024, 5, 27); // Memorial Day
        var nextTuesday = mondayAfterLongWeekend.AddDays(1);
        
        // Memorial Day Monday should not allow trading
        var memorialDayResult = SimulateMarketSessionLogic(mondayAfterLongWeekend.AddHours(14));
        var memorialDayTest = new TestResult
        {
            TestName = "Memorial Day Monday",
            TestTime = mondayAfterLongWeekend.AddHours(14),
            Expected = false,
            Actual = memorialDayResult,
            IsValid = false == memorialDayResult,
            Description = "Memorial Day Monday should not allow trading"
        };
        report.Results.Add(memorialDayTest);
        Console.WriteLine($"  Memorial Day: {(memorialDayTest.IsValid ? "✅" : "❌")} Expected=false, Got={memorialDayResult}");
        
        // Tuesday after Memorial Day should allow trading
        var tuesdayResult = SimulateMarketSessionLogic(nextTuesday.AddHours(14));
        var tuesdayTest = new TestResult
        {
            TestName = "Tuesday after Memorial Day",
            TestTime = nextTuesday.AddHours(14),
            Expected = true,
            Actual = tuesdayResult,
            IsValid = true == tuesdayResult,
            Description = "Tuesday after Memorial Day should allow trading"
        };
        report.Results.Add(tuesdayTest);
        Console.WriteLine($"  Tuesday after: {(tuesdayTest.IsValid ? "✅" : "❌")} Expected=true, Got={tuesdayResult}");
    }
    
    private static void ValidateDSTTransitions(ValidationReport report)
    {
        // 2024 DST transitions
        var springForward = new DateTime(2024, 3, 10); // Second Sunday in March
        var fallBack = new DateTime(2024, 11, 3);      // First Sunday in November
        
        // Test Monday after spring DST transition
        var mondayAfterSpring = springForward.AddDays(1);
        if (mondayAfterSpring.DayOfWeek == DayOfWeek.Monday)
        {
            var springResult = SimulateMarketSessionLogic(mondayAfterSpring.AddHours(14));
            var springTest = new TestResult
            {
                TestName = "Monday after Spring DST",
                TestTime = mondayAfterSpring.AddHours(14),
                Expected = true,
                Actual = springResult,
                IsValid = true == springResult,
                Description = "Monday after spring DST transition should allow trading"
            };
            report.Results.Add(springTest);
            Console.WriteLine($"  Spring DST Monday: {(springTest.IsValid ? "✅" : "❌")} Expected=true, Got={springResult}");
        }
        
        // Test Monday after fall DST transition
        var mondayAfterFall = fallBack.AddDays(1);
        if (mondayAfterFall.DayOfWeek == DayOfWeek.Monday)
        {
            var fallResult = SimulateMarketSessionLogic(mondayAfterFall.AddHours(14));
            var fallTest = new TestResult
            {
                TestName = "Monday after Fall DST",
                TestTime = mondayAfterFall.AddHours(14),
                Expected = true,
                Actual = fallResult,
                IsValid = true == fallResult,
                Description = "Monday after fall DST transition should allow trading"
            };
            report.Results.Add(fallTest);
            Console.WriteLine($"  Fall DST Monday: {(fallTest.IsValid ? "✅" : "❌")} Expected=true, Got={fallResult}");
        }
    }
    
    /// <summary>
    /// Simulates the market session logic that should be implemented in MarketSessionGuard
    /// This replicates the logic for validation purposes
    /// </summary>
    private static bool SimulateMarketSessionLogic(DateTime utcTime)
    {
        try
        {
            // Convert to Eastern Time
            var easternTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
            var easternTime = TimeZoneInfo.ConvertTimeFromUtc(utcTime, easternTimeZone);
            
            // Check if it's a weekday
            if (easternTime.DayOfWeek == DayOfWeek.Saturday || easternTime.DayOfWeek == DayOfWeek.Sunday)
            {
                return false;
            }
            
            // Check if it's a known holiday
            if (IsMarketHoliday(easternTime.Date))
            {
                return false;
            }
            
            // Check if it's during market hours (9:30 AM - 4:00 PM ET)
            var marketOpen = new TimeSpan(9, 30, 0);  // 9:30 AM ET
            var marketClose = new TimeSpan(16, 0, 0); // 4:00 PM ET
            var currentTime = easternTime.TimeOfDay;
            
            return currentTime >= marketOpen && currentTime <= marketClose;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in market session logic: {ex.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// Checks if a date is a known market holiday
    /// </summary>
    private static bool IsMarketHoliday(DateTime date)
    {
        // New Year's Day
        if (date.Month == 1 && date.Day == 1)
            return true;
            
        // Independence Day
        if (date.Month == 7 && date.Day == 4)
            return true;
            
        // Christmas Day
        if (date.Month == 12 && date.Day == 25)
            return true;
            
        // MLK Day (3rd Monday in January)
        if (date.Month == 1 && date.DayOfWeek == DayOfWeek.Monday)
        {
            var firstMonday = GetFirstMondayOfMonth(date.Year, 1);
            if (date == firstMonday.AddDays(14)) // 3rd Monday
                return true;
        }
        
        // Presidents' Day (3rd Monday in February)
        if (date.Month == 2 && date.DayOfWeek == DayOfWeek.Monday)
        {
            var firstMonday = GetFirstMondayOfMonth(date.Year, 2);
            if (date == firstMonday.AddDays(14)) // 3rd Monday
                return true;
        }
        
        // Memorial Day (last Monday in May)
        if (date.Month == 5 && date.DayOfWeek == DayOfWeek.Monday)
        {
            var lastMonday = GetLastMondayOfMonth(date.Year, 5);
            if (date == lastMonday)
                return true;
        }
        
        // Labor Day (1st Monday in September)
        if (date.Month == 9 && date.DayOfWeek == DayOfWeek.Monday)
        {
            var firstMonday = GetFirstMondayOfMonth(date.Year, 9);
            if (date == firstMonday)
                return true;
        }
        
        return false;
    }
    
    private static DateTime GetFirstMondayOfMonth(int year, int month)
    {
        var firstDay = new DateTime(year, month, 1);
        var daysUntilMonday = ((int)DayOfWeek.Monday - (int)firstDay.DayOfWeek + 7) % 7;
        return firstDay.AddDays(daysUntilMonday);
    }
    
    private static DateTime GetLastMondayOfMonth(int year, int month)
    {
        var lastDay = new DateTime(year, month, DateTime.DaysInMonth(year, month));
        var daysBackToMonday = ((int)lastDay.DayOfWeek - (int)DayOfWeek.Monday + 7) % 7;
        return lastDay.AddDays(-daysBackToMonday);
    }
    
    private static void GenerateFinalReport(ValidationReport report)
    {
        var totalTests = report.Results.Count;
        var passedTests = report.Results.Count(r => r.IsValid);
        var failedTests = totalTests - passedTests;
        
        Console.WriteLine($"\n📊 Final Validation Report:");
        Console.WriteLine($"  Total Tests: {totalTests}");
        Console.WriteLine($"  Passed: {passedTests} ✅");
        Console.WriteLine($"  Failed: {failedTests} ❌");
        Console.WriteLine($"  Success Rate: {(double)passedTests / totalTests:P1}");
        
        if (failedTests > 0)
        {
            Console.WriteLine($"\n❌ Failed Tests:");
            foreach (var failure in report.Results.Where(r => !r.IsValid))
            {
                Console.WriteLine($"  - {failure.TestName}: {failure.Description}");
                Console.WriteLine($"    Expected: {failure.Expected}, Got: {failure.Actual}");
                Console.WriteLine($"    Time: {failure.TestTime:yyyy-MM-dd HH:mm:ss} UTC");
            }
        }
        
        Console.WriteLine($"\n✅ Monday Market Session Validation Complete!");
        Console.WriteLine($"Validation completed at: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
    }
}

public class ValidationReport
{
    public List<TestResult> Results { get; set; } = new();
    public DateTime StartTime { get; set; } = DateTime.UtcNow;
    public DateTime? EndTime { get; set; }
}

public class TestResult
{
    public string TestName { get; set; } = string.Empty;
    public DateTime TestTime { get; set; }
    public bool Expected { get; set; }
    public bool Actual { get; set; }
    public bool IsValid { get; set; }
    public string Description { get; set; } = string.Empty;
}

// Entry point for standalone execution
public class Program
{
    public static void Main(string[] args)
    {
        try
        {
            var report = MondayMarketSessionValidator.ValidateMondayMarketSessions();
            
            // Exit with appropriate code
            var failedTests = report.Results.Count(r => !r.IsValid);
            Environment.Exit(failedTests > 0 ? 1 : 0);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Validation failed with exception: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            Environment.Exit(1);
        }
    }
}

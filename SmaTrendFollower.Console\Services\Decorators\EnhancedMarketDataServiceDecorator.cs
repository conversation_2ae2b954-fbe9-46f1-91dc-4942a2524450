using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Configuration;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Decorator that adds enhanced data retrieval capabilities to existing IMarketDataService
/// Provides backward compatibility while adding robust fallback strategies
/// </summary>
public sealed class EnhancedMarketDataServiceDecorator : IEnhancedMarketDataService, IDisposable
{
    private readonly IMarketDataService _innerService;
    private readonly IEnhancedDataRetrievalService _enhancedRetrieval;
    private readonly ILogger<EnhancedMarketDataServiceDecorator> _logger;
    private readonly EnhancedServicesOptions _options;

    public EnhancedMarketDataServiceDecorator(
        IMarketDataService innerService,
        IEnhancedDataRetrievalService enhancedRetrieval,
        ILogger<EnhancedMarketDataServiceDecorator> logger,
        EnhancedServicesOptions options)
    {
        _innerService = innerService;
        _enhancedRetrieval = enhancedRetrieval;
        _logger = logger;
        _options = options;
    }

    // === Enhanced Methods ===

    public async Task<DataRetrievalResult<IPage<IBar>>> GetStockBarsRobustlyAsync(
        string symbol, 
        DateTime startDate, 
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        if (!_options.EnableEnhancedDataRetrieval)
        {
            // Fallback to original service
            try
            {
                var data = await _innerService.GetStockBarsAsync(symbol, startDate, endDate);
                return new DataRetrievalResult<IPage<IBar>>
                {
                    IsSuccess = true,
                    Data = data,
                    FinalDataSource = DataSource.PrimaryApi,
                    DataQuality = DataQuality.High,
                    Attempts = new List<DataRetrievalAttempt>
                    {
                        new() { Source = DataSource.PrimaryApi, Success = true, StartTime = DateTime.UtcNow, EndTime = DateTime.UtcNow }
                    }
                };
            }
            catch (Exception ex)
            {
                return new DataRetrievalResult<IPage<IBar>>
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    Attempts = new List<DataRetrievalAttempt>
                    {
                        new() { Source = DataSource.PrimaryApi, Success = false, ErrorMessage = ex.Message, StartTime = DateTime.UtcNow, EndTime = DateTime.UtcNow }
                    }
                };
            }
        }

        return await _enhancedRetrieval.GetStockBarsAsync(symbol, startDate, endDate, cancellationToken);
    }

    public async Task<IDictionary<string, DataRetrievalResult<IPage<IBar>>>> GetStockBarsBatchRobustlyAsync(
        IEnumerable<string> symbols,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        if (!_options.EnableEnhancedDataRetrieval)
        {
            // Fallback to original service batch method
            var results = new Dictionary<string, DataRetrievalResult<IPage<IBar>>>();
            
            try
            {
                var batchData = await _innerService.GetStockBarsAsync(symbols, startDate, endDate);
                
                foreach (var (symbol, data) in batchData)
                {
                    results[symbol] = new DataRetrievalResult<IPage<IBar>>
                    {
                        IsSuccess = true,
                        Data = data,
                        FinalDataSource = DataSource.PrimaryApi,
                        DataQuality = DataQuality.High,
                        Attempts = new List<DataRetrievalAttempt>
                        {
                            new() { Source = DataSource.PrimaryApi, Success = true, StartTime = DateTime.UtcNow, EndTime = DateTime.UtcNow }
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                foreach (var symbol in symbols)
                {
                    results[symbol] = new DataRetrievalResult<IPage<IBar>>
                    {
                        IsSuccess = false,
                        ErrorMessage = ex.Message,
                        Attempts = new List<DataRetrievalAttempt>
                        {
                            new() { Source = DataSource.PrimaryApi, Success = false, ErrorMessage = ex.Message, StartTime = DateTime.UtcNow, EndTime = DateTime.UtcNow }
                        }
                    };
                }
            }
            
            return results;
        }

        return await _enhancedRetrieval.GetStockBarsBatchAsync(symbols, startDate, endDate, cancellationToken);
    }

    // === Delegate all original methods to inner service ===

    public Task<IPage<IBar>> GetStockBarsAsync(string symbol, DateTime startDate, DateTime endDate)
        => _innerService.GetStockBarsAsync(symbol, startDate, endDate);

    public Task<IPage<IBar>> GetStockMinuteBarsAsync(string symbol, DateTime startDate, DateTime endDate)
        => _innerService.GetStockMinuteBarsAsync(symbol, startDate, endDate);

    public Task<IDictionary<string, IPage<IBar>>> GetStockBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate)
        => _innerService.GetStockBarsAsync(symbols, startDate, endDate);

    public Task<IDictionary<string, IPage<IBar>>> GetStockMinuteBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate)
        => _innerService.GetStockMinuteBarsAsync(symbols, startDate, endDate);

    public Task<decimal?> GetIndexValueAsync(string indexSymbol)
        => _innerService.GetIndexValueAsync(indexSymbol);

    public Task<IEnumerable<IndexBar>> GetIndexBarsAsync(string indexSymbol, DateTime startDate, DateTime endDate)
        => _innerService.GetIndexBarsAsync(indexSymbol, startDate, endDate);

    public Task<IAccount> GetAccountAsync()
        => _innerService.GetAccountAsync();

    public Task<IReadOnlyList<IPosition>> GetPositionsAsync()
        => _innerService.GetPositionsAsync();

    public Task<IReadOnlyList<IOrder>> GetRecentFillsAsync(int limitCount = 100)
        => _innerService.GetRecentFillsAsync(limitCount);

    public Task<IOrder> SubmitOrderAsync(NewOrderRequest orderRequest)
        => _innerService.SubmitOrderAsync(orderRequest);

    public Task<IOrder> GetOrderAsync(Guid orderId)
        => _innerService.GetOrderAsync(orderId);

    public Task<bool> CancelOrderAsync(Guid orderId)
        => _innerService.CancelOrderAsync(orderId);



    public Task<IPage<IBar>> GetCryptoBarsAsync(string symbol, DateTime startDate, DateTime endDate)
        => _innerService.GetCryptoBarsAsync(symbol, startDate, endDate);

    public Task<IPage<IBar>> GetCryptoMinuteBarsAsync(string symbol, DateTime startDate, DateTime endDate)
        => _innerService.GetCryptoMinuteBarsAsync(symbol, startDate, endDate);

    public Task<IDictionary<string, IPage<IBar>>> GetCryptoBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate)
        => _innerService.GetCryptoBarsAsync(symbols, startDate, endDate);

    public Task<IDictionary<string, IPage<IBar>>> GetCryptoMinuteBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate)
        => _innerService.GetCryptoMinuteBarsAsync(symbols, startDate, endDate);

    public Task<IPage<IQuote>> GetStockQuotesAsync(string symbol, DateTime startDate, DateTime endDate)
        => _innerService.GetStockQuotesAsync(symbol, startDate, endDate);

    public Task<IPage<ITrade>> GetStockTradesAsync(string symbol, DateTime startDate, DateTime endDate)
        => _innerService.GetStockTradesAsync(symbol, startDate, endDate);

    public Task<ISnapshot> GetSnapshotAsync(string symbol)
        => _innerService.GetSnapshotAsync(symbol);

    public Task<IDictionary<string, ISnapshot>> GetSnapshotsAsync(IEnumerable<string> symbols)
        => _innerService.GetSnapshotsAsync(symbols);

    public Task<IPage<IOptionContract>> GetOptionContractsAsync(OptionContractsRequest request)
        => _innerService.GetOptionContractsAsync(request);

    public Task<IPage<IBar>> GetOptionBarsAsync(string symbol, DateTime startDate, DateTime endDate)
        => _innerService.GetOptionBarsAsync(symbol, startDate, endDate);

    public Task<IPage<IQuote>> GetOptionQuotesAsync(string symbol, DateTime startDate, DateTime endDate)
        => _innerService.GetOptionQuotesAsync(symbol, startDate, endDate);

    public Task<IPage<ITrade>> GetOptionTradesAsync(string symbol, DateTime startDate, DateTime endDate)
        => _innerService.GetOptionTradesAsync(symbol, startDate, endDate);

    public Task<ISnapshot> GetOptionSnapshotAsync(string symbol)
        => _innerService.GetOptionSnapshotAsync(symbol);

    public Task<IDictionary<string, ISnapshot>> GetOptionSnapshotsAsync(IEnumerable<string> symbols)
        => _innerService.GetOptionSnapshotsAsync(symbols);

    public Task<IPage<IBar>> GetForexBarsAsync(string symbol, DateTime startDate, DateTime endDate)
        => _innerService.GetForexBarsAsync(symbol, startDate, endDate);

    public Task<IPage<IQuote>> GetForexQuotesAsync(string symbol, DateTime startDate, DateTime endDate)
        => _innerService.GetForexQuotesAsync(symbol, startDate, endDate);

    public Task<ISnapshot> GetForexSnapshotAsync(string symbol)
        => _innerService.GetForexSnapshotAsync(symbol);

    public Task<IDictionary<string, ISnapshot>> GetForexSnapshotsAsync(IEnumerable<string> symbols)
        => _innerService.GetForexSnapshotsAsync(symbols);

    public Task<IPage<INewsArticle>> GetNewsAsync(NewsArticlesRequest request)
        => _innerService.GetNewsAsync(request);

    public Task<decimal> GetCurrentVixAsync()
        => _innerService.GetCurrentVixAsync();



    // === Missing IMarketDataService methods ===

    public Task<IEnumerable<OptionData>> GetOptionsDataAsync(string underlyingSymbol, DateTime? expirationDate = null)
        => _innerService.GetOptionsDataAsync(underlyingSymbol, expirationDate);

    public Task<IEnumerable<OptionData>> GetOptionsDataAsync(string underlyingSymbol, DateTime? expirationDate = null, decimal? currentPrice = null, double? strikeBandPercent = null)
        => _innerService.GetOptionsDataAsync(underlyingSymbol, expirationDate, currentPrice, strikeBandPercent);

    public Task<IEnumerable<VixTermData>> GetVixTermStructureAsync()
        => _innerService.GetVixTermStructureAsync();

    public Task<IEnumerable<OptionQuote>> GetOptionsQuotesAsync(IEnumerable<string> optionSymbols)
        => _innerService.GetOptionsQuotesAsync(optionSymbols);

    public Task<IEnumerable<OptionData>> GetProtectivePutOptionsAsync(string underlyingSymbol, int daysToExpiration = 30)
        => _innerService.GetProtectivePutOptionsAsync(underlyingSymbol, daysToExpiration);

    public Task<IEnumerable<OptionData>> GetCoveredCallOptionsAsync(string underlyingSymbol, decimal currentPrice, int daysToExpiration = 7)
        => _innerService.GetCoveredCallOptionsAsync(underlyingSymbol, currentPrice, daysToExpiration);

    public Task<IEnumerable<string>> GetUniverseWithAdvFilterAsync(decimal minAdv = 20_000_000m)
        => _innerService.GetUniverseWithAdvFilterAsync(minAdv);

    public Task<VixAnalysis> GetVixAnalysisAsync()
        => _innerService.GetVixAnalysisAsync();

    public Task<bool> IsVixSpikeAsync(decimal threshold = 25.0m)
        => _innerService.IsVixSpikeAsync(threshold);

    public void Dispose()
    {
        if (_innerService is IDisposable disposableInner)
        {
            disposableInner.Dispose();
        }
    }
}

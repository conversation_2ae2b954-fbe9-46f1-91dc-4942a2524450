using Microsoft.Extensions.Logging;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Monitoring;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Adaptive rate limiting service that adjusts limits based on API response patterns
/// Implements circuit breaker patterns and intelligent batching
/// </summary>
public sealed class AdaptiveRateLimitingService : IAdaptiveRateLimitingService, IDisposable
{
    private readonly ILogger<AdaptiveRateLimitingService> _logger;
    private readonly AdaptiveRateLimitConfiguration _config;
    private readonly ConcurrentDictionary<string, ApiProviderState> _providerStates = new();
    private readonly Timer _adjustmentTimer;
    private readonly SemaphoreSlim _adjustmentSemaphore = new(1, 1);

    public AdaptiveRateLimitingService(
        ILogger<AdaptiveRateLimitingService> logger,
        AdaptiveRateLimitConfiguration config)
    {
        _logger = logger;
        _config = config;
        
        // Initialize provider states
        foreach (var provider in _config.Providers.Keys)
        {
            _providerStates[provider] = new ApiProviderState(_config.Providers[provider]);
        }

        // Start adjustment timer
        _adjustmentTimer = new Timer(AdjustRateLimits, null, 
            _config.AdjustmentInterval, _config.AdjustmentInterval);
    }

    /// <summary>
    /// Attempts to acquire a permit for an API call with adaptive rate limiting
    /// </summary>
    public async Task<RateLimitResult> TryAcquireAsync(
        string provider, 
        string operation, 
        int priority = 1,
        CancellationToken cancellationToken = default)
    {
        if (!_providerStates.TryGetValue(provider, out var state))
        {
            _logger.LogWarning("Unknown provider {Provider}, allowing request", provider);
            return RateLimitResult.Success();
        }

        var stopwatch = Stopwatch.StartNew();

        // Check circuit breaker
        if (state.CircuitBreaker.IsOpen)
        {
            var timeUntilReset = state.CircuitBreaker.TimeUntilReset;
            _logger.LogDebug("Circuit breaker open for {Provider}, time until reset: {Time}", 
                provider, timeUntilReset);
            
            MetricsRegistry.CircuitBreakerTrips.WithLabels(provider, "open").Inc();
            return RateLimitResult.CircuitBreakerOpen(timeUntilReset);
        }

        // Try to acquire semaphore with timeout
        var acquired = await state.Semaphore.WaitAsync(_config.AcquisitionTimeout, cancellationToken);
        
        if (!acquired)
        {
            _logger.LogDebug("Rate limit acquisition timeout for {Provider}", provider);
            MetricsRegistry.RateLimitHits.WithLabels(provider, operation).Inc();
            return RateLimitResult.Timeout();
        }

        try
        {
            // Update statistics
            state.RecordRequest(operation, priority);
            
            stopwatch.Stop();
            MetricsRegistry.RateLimitAcquisitionDuration
                .WithLabels(provider)
                .Observe(stopwatch.Elapsed.TotalSeconds);

            return RateLimitResult.Success();
        }
        catch (Exception ex)
        {
            // Release semaphore on error
            state.Semaphore.Release();
            _logger.LogError(ex, "Error in rate limit acquisition for {Provider}", provider);
            return RateLimitResult.Error(ex.Message);
        }
    }

    /// <summary>
    /// Releases a permit and records the result for adaptive adjustment
    /// </summary>
    public void Release(string provider, string operation, bool success, TimeSpan duration, string? errorCode = null)
    {
        if (!_providerStates.TryGetValue(provider, out var state))
        {
            _logger.LogWarning("Unknown provider {Provider} for release", provider);
            return;
        }

        try
        {
            // Release semaphore
            state.Semaphore.Release();

            // Record result for adaptive adjustment
            state.RecordResult(operation, success, duration, errorCode);

            // Update circuit breaker
            if (success)
            {
                state.CircuitBreaker.RecordSuccess();
            }
            else
            {
                state.CircuitBreaker.RecordFailure();
                
                // Check for rate limit errors
                if (IsRateLimitError(errorCode))
                {
                    state.RecordRateLimitHit();
                    _logger.LogWarning("Rate limit hit detected for {Provider}: {Error}", provider, errorCode);
                }
            }

            // Record metrics
            MetricsRegistry.ApiRequestDuration
                .WithLabels(provider, operation, success ? "success" : "failure")
                .Observe(duration.TotalSeconds);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in rate limit release for {Provider}", provider);
        }
    }

    /// <summary>
    /// Gets current rate limit statistics for a provider
    /// </summary>
    public RateLimitStats GetStats(string provider)
    {
        if (!_providerStates.TryGetValue(provider, out var state))
        {
            return new RateLimitStats(provider, 0, 0, 0, false, TimeSpan.Zero);
        }

        return new RateLimitStats(
            provider,
            state.CurrentLimit,
            state.Semaphore.CurrentCount,
            state.Statistics.TotalRequests,
            state.CircuitBreaker.IsOpen,
            state.CircuitBreaker.TimeUntilReset);
    }

    /// <summary>
    /// Creates an intelligent batch processor for multiple operations
    /// </summary>
    public IBatchProcessor<T> CreateBatchProcessor<T>(
        string provider,
        Func<IEnumerable<T>, CancellationToken, Task<IDictionary<T, bool>>> batchOperation,
        BatchProcessorOptions? options = null)
    {
        return new IntelligentBatchProcessor<T>(this, provider, batchOperation, options ?? new(), _logger);
    }

    private async void AdjustRateLimits(object? state)
    {
        if (!await _adjustmentSemaphore.WaitAsync(100))
            return;

        try
        {
            foreach (var (provider, providerState) in _providerStates)
            {
                await AdjustProviderRateLimit(provider, providerState);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during rate limit adjustment");
        }
        finally
        {
            _adjustmentSemaphore.Release();
        }
    }

    private async Task AdjustProviderRateLimit(string provider, ApiProviderState state)
    {
        var stats = state.Statistics;
        var config = _config.Providers[provider];

        // Calculate success rate over the adjustment window
        var recentSuccessRate = stats.GetRecentSuccessRate(_config.AdjustmentWindow);
        var recentRateLimitHits = stats.GetRecentRateLimitHits(_config.AdjustmentWindow);

        var oldLimit = state.CurrentLimit;
        var newLimit = oldLimit;

        // Adjust based on success rate and rate limit hits
        if (recentRateLimitHits > 0)
        {
            // Decrease limit if we're hitting rate limits
            var decreaseFactor = Math.Min(0.5, recentRateLimitHits * 0.1);
            newLimit = Math.Max(config.MinLimit, (int)(oldLimit * (1 - decreaseFactor)));
            _logger.LogInformation("Decreasing rate limit for {Provider} due to {Hits} rate limit hits: {Old} -> {New}",
                provider, recentRateLimitHits, oldLimit, newLimit);
        }
        else if (recentSuccessRate > _config.SuccessRateThreshold)
        {
            // Increase limit if success rate is high and no rate limit hits
            var increaseFactor = Math.Min(0.2, (recentSuccessRate - _config.SuccessRateThreshold) * 0.5);
            newLimit = Math.Min(config.MaxLimit, (int)(oldLimit * (1 + increaseFactor)));
            
            if (newLimit > oldLimit)
            {
                _logger.LogDebug("Increasing rate limit for {Provider} due to high success rate ({Rate:P2}): {Old} -> {New}",
                    provider, recentSuccessRate, oldLimit, newLimit);
            }
        }

        // Apply the new limit
        if (newLimit != oldLimit)
        {
            await state.UpdateLimitAsync(newLimit);
            
            MetricsRegistry.RateLimitAdjustments
                .WithLabels(provider, newLimit > oldLimit ? "increase" : "decrease")
                .Inc();
        }

        // Log current statistics
        _logger.LogDebug("Rate limit stats for {Provider}: Limit={Limit}, Available={Available}, Success={Success:P2}, RateLimitHits={Hits}",
            provider, newLimit, state.Semaphore.CurrentCount, recentSuccessRate, recentRateLimitHits);
    }

    private static bool IsRateLimitError(string? errorCode)
    {
        if (string.IsNullOrEmpty(errorCode))
            return false;

        return errorCode.Contains("429") ||
               errorCode.Contains("TooManyRequests") ||
               errorCode.Contains("rate limit", StringComparison.OrdinalIgnoreCase) ||
               errorCode.Contains("throttle", StringComparison.OrdinalIgnoreCase);
    }

    public void Dispose()
    {
        _adjustmentTimer?.Dispose();
        _adjustmentSemaphore?.Dispose();
        
        foreach (var state in _providerStates.Values)
        {
            state.Dispose();
        }
    }
}

/// <summary>
/// Result of a rate limit acquisition attempt
/// </summary>
public sealed class RateLimitResult
{
    public bool IsSuccess { get; private set; }
    public string? ErrorMessage { get; private set; }
    public TimeSpan? RetryAfter { get; private set; }
    public RateLimitResultType Type { get; private set; }

    private RateLimitResult() { }

    public static RateLimitResult Success() => new() { IsSuccess = true, Type = RateLimitResultType.Success };
    public static RateLimitResult Timeout() => new() { IsSuccess = false, Type = RateLimitResultType.Timeout, ErrorMessage = "Acquisition timeout" };
    public static RateLimitResult CircuitBreakerOpen(TimeSpan retryAfter) => new() { IsSuccess = false, Type = RateLimitResultType.CircuitBreakerOpen, RetryAfter = retryAfter, ErrorMessage = "Circuit breaker open" };
    public static RateLimitResult Error(string message) => new() { IsSuccess = false, Type = RateLimitResultType.Error, ErrorMessage = message };
}

public enum RateLimitResultType
{
    Success,
    Timeout,
    CircuitBreakerOpen,
    Error
}

/// <summary>
/// Statistics for rate limiting
/// </summary>
public sealed class RateLimitStats
{
    public string Provider { get; }
    public int CurrentLimit { get; }
    public int AvailablePermits { get; }
    public long TotalRequests { get; }
    public bool CircuitBreakerOpen { get; }
    public TimeSpan TimeUntilCircuitBreakerReset { get; }

    public RateLimitStats(string provider, int currentLimit, int availablePermits, long totalRequests, bool circuitBreakerOpen, TimeSpan timeUntilReset)
    {
        Provider = provider;
        CurrentLimit = currentLimit;
        AvailablePermits = availablePermits;
        TotalRequests = totalRequests;
        CircuitBreakerOpen = circuitBreakerOpen;
        TimeUntilCircuitBreakerReset = timeUntilReset;
    }
}

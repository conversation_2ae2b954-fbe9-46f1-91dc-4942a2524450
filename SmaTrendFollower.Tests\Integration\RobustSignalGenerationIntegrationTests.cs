using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FluentAssertions;
using Moq;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using SmaTrendFollower.Configuration;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// Integration tests for robust signal generation with comprehensive error recovery
/// </summary>
public sealed class RobustSignalGenerationIntegrationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly Mock<IAdaptiveSignalGenerator> _mockAdaptiveGenerator;
    private readonly Mock<ISignalGenerator> _mockFallbackGenerator;
    private readonly Mock<IFlexibleDataStalenessService> _mockStalenessService;
    private readonly IRobustSignalGenerationService _robustService;

    public RobustSignalGenerationIntegrationTests()
    {
        var services = new ServiceCollection();
        
        // Setup mocks
        _mockAdaptiveGenerator = new Mock<IAdaptiveSignalGenerator>();
        _mockFallbackGenerator = new Mock<ISignalGenerator>();
        _mockStalenessService = new Mock<IFlexibleDataStalenessService>();

        // Configure services
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        services.AddSingleton(_mockAdaptiveGenerator.Object);
        services.AddSingleton(_mockFallbackGenerator.Object);
        services.AddSingleton(_mockStalenessService.Object);
        
        services.AddSingleton<IRobustSignalGenerationService, RobustSignalGenerationService>();
        services.AddSingleton(new RobustSignalConfiguration
        {
            MaxConcurrentGenerations = 5,
            MinimumAcceptableSignals = 3,
            MinimumConfidenceScore = 0.5,
            EnableFallbackGeneration = true,
            EnableEmergencyGeneration = true,
            AdaptiveGenerationTimeout = TimeSpan.FromSeconds(30),
            FallbackGenerationTimeout = TimeSpan.FromSeconds(15),
            CoreSymbols = new List<string> { "SPY", "QQQ", "AAPL" }
        });

        _serviceProvider = services.BuildServiceProvider();
        _robustService = _serviceProvider.GetRequiredService<IRobustSignalGenerationService>();
    }

    [Fact]
    public async Task GenerateSignalsRobustlyAsync_AdaptiveSuccess_ReturnsAdaptiveSignals()
    {
        // Arrange
        var adaptiveSignals = CreateMockAdaptiveSignals(5);
        
        _mockAdaptiveGenerator.Setup(x => x.GenerateSignalsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(adaptiveSignals);

        // Act
        var result = await _robustService.GenerateSignalsRobustlyAsync(5);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Signals.Should().HaveCount(5);
        result.GenerationMethods.Should().Contain("Adaptive");
        result.SuccessRate.Should().Be(1.0);
        result.ErrorMessage.Should().BeNull();
        
        _mockFallbackGenerator.Verify(x => x.GenerateSignalsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()), 
            Times.Never);
    }

    [Fact]
    public async Task GenerateSignalsRobustlyAsync_AdaptivePartialSuccess_UsesFallback()
    {
        // Arrange
        var partialAdaptiveSignals = CreateMockAdaptiveSignals(2); // Only 2 out of 5 requested
        var fallbackSignals = CreateMockTradingSignals(3);

        _mockAdaptiveGenerator.Setup(x => x.GenerateSignalsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(partialAdaptiveSignals);

        _mockFallbackGenerator.Setup(x => x.GenerateSignalsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fallbackSignals);

        _mockStalenessService.Setup(x => x.SetPolicyAsync(StalenessPolicy.Relaxed, It.IsAny<string>()))
            .Returns(Task.CompletedTask);

        _mockStalenessService.Setup(x => x.SetPolicyAsync(StalenessPolicy.Standard, It.IsAny<string>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _robustService.GenerateSignalsRobustlyAsync(5);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Signals.Should().HaveCount(5);
        result.GenerationMethods.Should().Contain("Adaptive");
        result.GenerationMethods.Should().Contain("Fallback");
        result.SuccessRate.Should().BeGreaterThan(0.5);
        
        _mockStalenessService.Verify(x => x.SetPolicyAsync(StalenessPolicy.Relaxed, "Fallback signal generation"), 
            Times.Once);
        _mockStalenessService.Verify(x => x.SetPolicyAsync(StalenessPolicy.Standard, "Fallback generation completed"), 
            Times.Once);
    }

    [Fact]
    public async Task GenerateSignalsRobustlyAsync_AdaptiveFails_UsesFallbackOnly()
    {
        // Arrange
        var fallbackSignals = CreateMockTradingSignals(4);

        _mockAdaptiveGenerator.Setup(x => x.GenerateSignalsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Adaptive generation failed"));

        _mockFallbackGenerator.Setup(x => x.GenerateSignalsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fallbackSignals);

        _mockStalenessService.Setup(x => x.SetPolicyAsync(It.IsAny<StalenessPolicy>(), It.IsAny<string>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _robustService.GenerateSignalsRobustlyAsync(5);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Signals.Should().HaveCount(4);
        result.GenerationMethods.Should().Contain("Fallback");
        result.GenerationMethods.Should().NotContain("Adaptive");
        result.Warnings.Should().NotBeEmpty();
        result.ErrorMessage.Should().Contain("Adaptive generation failed");
    }

    [Fact]
    public async Task GenerateSignalsRobustlyAsync_BothFail_UsesEmergencyGeneration()
    {
        // Arrange
        var emergencySignals = CreateMockTradingSignals(2, new[] { "SPY", "QQQ" });

        _mockAdaptiveGenerator.Setup(x => x.GenerateSignalsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Adaptive generation failed"));

        _mockFallbackGenerator.Setup(x => x.GenerateSignalsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Fallback generation failed"));

        // Setup emergency generation for core symbols
        _mockFallbackGenerator.Setup(x => x.GenerateSignalsAsync(1, It.IsAny<CancellationToken>()))
            .ReturnsAsync((int count, CancellationToken ct) => 
            {
                // Return different signals based on call order
                var callCount = _mockFallbackGenerator.Invocations.Count(i => i.Method.Name == "GenerateSignalsAsync" && (int)i.Arguments[0] == 1);
                return callCount switch
                {
                    1 => new[] { CreateMockTradingSignal("SPY") },
                    2 => new[] { CreateMockTradingSignal("QQQ") },
                    _ => Array.Empty<TradingSignal>()
                };
            });

        _mockStalenessService.Setup(x => x.ActivateEmergencyModeAsync(It.IsAny<string>(), It.IsAny<TimeSpan>()))
            .Returns(Task.CompletedTask);

        _mockStalenessService.Setup(x => x.SetPolicyAsync(It.IsAny<StalenessPolicy>(), It.IsAny<string>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _robustService.GenerateSignalsRobustlyAsync(5);

        // Assert
        result.IsSuccess.Should().BeFalse(); // Less than minimum acceptable signals (3)
        result.Signals.Should().HaveCountLessThan(3);
        result.GenerationMethods.Should().Contain("Emergency");
        result.Warnings.Should().HaveCountGreaterThan(0);
        
        _mockStalenessService.Verify(x => x.ActivateEmergencyModeAsync("Emergency signal generation", It.IsAny<TimeSpan>()), 
            Times.Once);
    }

    [Fact]
    public async Task GenerateSignalsRobustlyAsync_Timeout_ReturnsPartialResults()
    {
        // Arrange
        _mockAdaptiveGenerator.Setup(x => x.GenerateSignalsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .Returns(async (int count, CancellationToken ct) =>
            {
                await Task.Delay(TimeSpan.FromMinutes(1), ct); // Longer than timeout
                return CreateMockAdaptiveSignals(count);
            });

        // Act
        var result = await _robustService.GenerateSignalsRobustlyAsync(5);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.ErrorMessage.Should().Contain("timeout");
        result.Warnings.Should().Contain(w => w.Contains("timed out"));
    }

    [Fact]
    public async Task GenerateSignalsRobustlyAsync_LowConfidenceSignals_FiltersAndUsesFallback()
    {
        // Arrange
        var lowConfidenceSignals = CreateMockAdaptiveSignals(3, confidenceScore: 0.3); // Below minimum 0.5
        var fallbackSignals = CreateMockTradingSignals(4);

        _mockAdaptiveGenerator.Setup(x => x.GenerateSignalsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(lowConfidenceSignals);

        _mockFallbackGenerator.Setup(x => x.GenerateSignalsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fallbackSignals);

        _mockStalenessService.Setup(x => x.SetPolicyAsync(It.IsAny<StalenessPolicy>(), It.IsAny<string>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _robustService.GenerateSignalsRobustlyAsync(5);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Signals.Should().HaveCount(4); // Only fallback signals since adaptive were filtered out
        result.GenerationMethods.Should().Contain("Fallback");
        result.SuccessRate.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task GetErrorStatistics_TracksErrorsCorrectly()
    {
        // Arrange - Force some errors
        _mockAdaptiveGenerator.Setup(x => x.GenerateSignalsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Test error"));

        _mockFallbackGenerator.Setup(x => x.GenerateSignalsAsync(It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Test error"));

        _mockStalenessService.Setup(x => x.SetPolicyAsync(It.IsAny<StalenessPolicy>(), It.IsAny<string>()))
            .Returns(Task.CompletedTask);

        _mockStalenessService.Setup(x => x.ActivateEmergencyModeAsync(It.IsAny<string>(), It.IsAny<TimeSpan>()))
            .Returns(Task.CompletedTask);

        // Act
        await _robustService.GenerateSignalsRobustlyAsync(5);
        var stats = _robustService.GetErrorStatistics();

        // Assert
        stats.Should().NotBeNull();
        // Note: Error statistics tracking would need to be implemented in the actual service
        // This test validates the interface exists and can be called
    }

    private static List<AdaptiveTradingSignal> CreateMockAdaptiveSignals(int count, double confidenceScore = 0.8)
    {
        var signals = new List<AdaptiveTradingSignal>();
        var symbols = new[] { "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX", "CRM", "ADBE" };
        
        for (int i = 0; i < count; i++)
        {
            signals.Add(new AdaptiveTradingSignal
            {
                Symbol = symbols[i % symbols.Length],
                Price = 100 + i * 10,
                IsValid = true,
                ConfidenceScore = confidenceScore,
                AdaptedReturn = 0.05m + i * 0.01m,
                Atr = 2.5m + i * 0.5m,
                Rsi = 50 + i * 2,
                DataSource = "Test",
                DataQuality = DataQuality.High,
                Strategy = SignalStrategy.FullSMA,
                ValidationTimestamp = DateTime.UtcNow
            });
        }
        
        return signals;
    }

    private static List<TradingSignal> CreateMockTradingSignals(int count, string[]? specificSymbols = null)
    {
        var signals = new List<TradingSignal>();
        var symbols = specificSymbols ?? new[] { "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX", "CRM", "ADBE" };
        
        for (int i = 0; i < count; i++)
        {
            signals.Add(CreateMockTradingSignal(symbols[i % symbols.Length], i));
        }
        
        return signals;
    }

    private static TradingSignal CreateMockTradingSignal(string symbol, int index = 0)
    {
        return new TradingSignal
        {
            Symbol = symbol,
            Price = 100 + index * 10,
            SixMonthReturn = 0.05m + index * 0.01m,
            Atr = 2.5m + index * 0.5m,
            Rsi = 50 + index * 2
        };
    }

    public void Dispose()
    {
        _robustService?.Dispose();
        _serviceProvider?.Dispose();
    }
}

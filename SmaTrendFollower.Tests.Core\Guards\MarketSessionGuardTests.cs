using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Logging.Abstractions;
using SmaTrendFollower.Services;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Guards;

public class MarketSessionGuardTests
{
    private readonly MarketSessionGuard _guard;

    public MarketSessionGuardTests()
    {
        var timeProvider = new TestTimeProvider();
        _guard = new MarketSessionGuard(timeProvider, new NullLogger<MarketSessionGuard>());
    }

    [Theory]
    [InlineData("2025-06-27T14:00:00Z", true)]   // Friday market open
    [InlineData("2025-06-28T14:00:00Z", false)]  // Saturday
    [InlineData("2025-06-29T14:00:00Z", false)]  // Sunday
    [InlineData("2025-06-30T14:00:00Z", true)]   // Monday market open
    public async Task CanTradeNowAsync_Works(string iso, bool expected)
    {
        // Arrange
        var testTime = DateTime.Parse(iso, null, System.Globalization.DateTimeStyles.AdjustToUniversal);
        var timeProvider = new TestTimeProvider(testTime);
        var guard = new MarketSessionGuard(timeProvider, new NullLogger<MarketSessionGuard>());

        // Act
        bool result = await guard.CanTradeNowAsync();

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public async Task CanTradeNowAsync_Weekend_SetsReasonCorrectly()
    {
        // Arrange - Saturday
        var testTime = DateTime.Parse("2025-06-28T14:00:00Z", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
        var timeProvider = new TestTimeProvider(testTime);
        var guard = new MarketSessionGuard(timeProvider, new NullLogger<MarketSessionGuard>());

        // Act
        bool result = await guard.CanTradeNowAsync();

        // Assert
        result.Should().BeFalse();
        guard.Reason.Should().Be("Weekend - markets closed");
    }

    [Fact]
    public async Task CanTradeNowAsync_Weekday_ClearsReason()
    {
        // Arrange - Friday
        var testTime = DateTime.Parse("2025-06-27T14:00:00Z", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
        var timeProvider = new TestTimeProvider(testTime);
        var guard = new MarketSessionGuard(timeProvider, new NullLogger<MarketSessionGuard>());

        // Act
        bool result = await guard.CanTradeNowAsync();

        // Assert
        result.Should().BeTrue();
        guard.Reason.Should().BeEmpty();
    }

    /// <summary>
    /// Comprehensive test for Monday market hours validation using historical data patterns
    /// Tests various Monday scenarios including holidays, early/late hours, and edge cases
    /// </summary>
    [Theory]
    [InlineData("2024-01-01T14:00:00Z", false)] // New Year's Day Monday (Holiday)
    [InlineData("2024-01-15T14:00:00Z", false)] // MLK Day Monday (3rd Monday in January)
    [InlineData("2024-02-19T14:00:00Z", false)] // Presidents Day Monday (3rd Monday in February)
    [InlineData("2024-05-27T14:00:00Z", false)] // Memorial Day Monday (Last Monday in May)
    [InlineData("2024-09-02T14:00:00Z", false)] // Labor Day Monday (1st Monday in September)
    [InlineData("2024-01-08T14:00:00Z", true)]  // Regular Monday market open
    [InlineData("2024-01-08T13:00:00Z", false)] // Monday before market open (9:00 AM ET)
    [InlineData("2024-01-08T21:30:00Z", false)] // Monday after market close (4:30 PM ET)
    [InlineData("2024-01-08T14:30:00Z", true)]  // Monday market open (9:30 AM ET)
    [InlineData("2024-01-08T21:00:00Z", true)]  // Monday market close (4:00 PM ET)
    public async Task CanTradeNowAsync_MondayHistoricalValidation(string iso, bool expected)
    {
        // Arrange
        var testTime = DateTime.Parse(iso, null, System.Globalization.DateTimeStyles.AdjustToUniversal);
        var timeProvider = new TestTimeProvider(testTime);
        var guard = new MarketSessionGuard(timeProvider, new NullLogger<MarketSessionGuard>());

        // Act
        bool result = await guard.CanTradeNowAsync();

        // Assert
        result.Should().Be(expected,
            $"Expected {expected} for {testTime:yyyy-MM-dd HH:mm:ss} UTC ({testTime.DayOfWeek})");
    }

    /// <summary>
    /// Tests Monday market hours across different time zones and DST transitions
    /// </summary>
    [Theory]
    [InlineData("2024-03-11T13:30:00Z", false)] // Monday before DST transition (8:30 AM ET)
    [InlineData("2024-03-11T14:30:00Z", true)]  // Monday after DST transition (9:30 AM ET)
    [InlineData("2024-11-04T14:30:00Z", true)]  // Monday before EST transition (9:30 AM ET)
    [InlineData("2024-11-04T22:00:00Z", true)]  // Monday EST transition (4:00 PM ET)
    public async Task CanTradeNowAsync_MondayDSTTransitions(string iso, bool expected)
    {
        // Arrange
        var testTime = DateTime.Parse(iso, null, System.Globalization.DateTimeStyles.AdjustToUniversal);
        var timeProvider = new TestTimeProvider(testTime);
        var guard = new MarketSessionGuard(timeProvider, new NullLogger<MarketSessionGuard>());

        // Act
        bool result = await guard.CanTradeNowAsync();

        // Assert
        result.Should().Be(expected,
            $"Expected {expected} for {testTime:yyyy-MM-dd HH:mm:ss} UTC during DST transition");
    }

    /// <summary>
    /// Validates Monday market session logic against a full year of historical data
    /// This test ensures consistency across all Mondays in a year
    /// </summary>
    [Fact]
    public async Task CanTradeNowAsync_FullYearMondayValidation()
    {
        // Arrange - Test all Mondays in 2024
        var year = 2024;
        var mondayResults = new List<(DateTime Date, bool CanTrade, string Reason)>();

        // Get all Mondays in 2024
        var startDate = new DateTime(year, 1, 1);
        var endDate = new DateTime(year, 12, 31);

        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            if (date.DayOfWeek == DayOfWeek.Monday)
            {
                // Test at 2:00 PM ET (market open time)
                var testTime = date.Date.AddHours(19); // 2:00 PM ET = 7:00 PM UTC (EST)
                var timeProvider = new TestTimeProvider(testTime);
                var guard = new MarketSessionGuard(timeProvider, new NullLogger<MarketSessionGuard>());

                var canTrade = await guard.CanTradeNowAsync();
                mondayResults.Add((date, canTrade, guard.Reason));
            }
        }

        // Assert - Validate results
        mondayResults.Should().HaveCountGreaterThan(50, "Should have tested all Mondays in the year");

        // Check known holidays are correctly identified as non-trading days
        var holidayMondays = mondayResults.Where(r => !r.CanTrade).ToList();
        var tradingMondays = mondayResults.Where(r => r.CanTrade).ToList();

        // Verify we have both trading and non-trading Mondays
        holidayMondays.Should().NotBeEmpty("Should have some Monday holidays");
        tradingMondays.Should().NotBeEmpty("Should have some trading Mondays");

        // Log results for analysis
        foreach (var result in mondayResults.Where(r => !r.CanTrade))
        {
            // These should be holidays or other non-trading days
            result.Reason.Should().NotBeEmpty($"Non-trading Monday {result.Date:yyyy-MM-dd} should have a reason");
        }
    }

/// <summary>
/// Test implementation of ITimeProvider for controlled time testing
/// </summary>
public class TestTimeProvider : ITimeProvider
{
    private readonly DateTime _fixedTime;

    public TestTimeProvider(DateTime? fixedTime = null)
    {
        _fixedTime = fixedTime ?? DateTime.UtcNow;
    }

    public DateTime UtcNow => _fixedTime;
}

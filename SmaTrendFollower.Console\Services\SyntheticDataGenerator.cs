using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Configuration;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for synthetic data generation
/// </summary>
public interface ISyntheticDataGenerator
{
    Task<IPage<IBar>> GenerateBarsAsync(string symbol, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);
}

/// <summary>
/// Generates synthetic market data when real data is unavailable
/// Uses market indices and sector correlations to create realistic estimates
/// </summary>
public sealed class SyntheticDataGenerator : ISyntheticDataGenerator
{
    private readonly IMarketDataService _marketDataService;
    private readonly IStockBarCacheService _cacheService;
    private readonly ILogger<SyntheticDataGenerator> _logger;
    private readonly SyntheticDataConfiguration _config;

    public SyntheticDataGenerator(
        IMarketDataService marketDataService,
        IStockBarCacheService cacheService,
        ILogger<SyntheticDataGenerator> logger,
        SyntheticDataConfiguration config)
    {
        _marketDataService = marketDataService;
        _cacheService = cacheService;
        _logger = logger;
        _config = config;
    }

    /// <summary>
    /// Generates synthetic bars based on market indices and historical patterns
    /// </summary>
    public async Task<IPage<IBar>> GenerateBarsAsync(
        string symbol, 
        DateTime startDate, 
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generating synthetic data for {Symbol} from {StartDate} to {EndDate}", 
            symbol, startDate, endDate);

        try
        {
            // Step 1: Get reference data (SPY as market proxy)
            var referenceData = await GetReferenceDataAsync(startDate, endDate, cancellationToken);
            if (referenceData == null || !referenceData.Any())
            {
                throw new InvalidOperationException("Cannot generate synthetic data without reference market data");
            }

            // Step 2: Get historical correlation data for the symbol
            var correlationData = await GetHistoricalCorrelationAsync(symbol, cancellationToken);

            // Step 3: Generate synthetic bars
            var syntheticBars = GenerateSyntheticBars(symbol, referenceData, correlationData, startDate, endDate);

            _logger.LogInformation("Generated {Count} synthetic bars for {Symbol}", syntheticBars.Count, symbol);

            return new SyntheticBarPage(syntheticBars, symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate synthetic data for {Symbol}", symbol);
            throw;
        }
    }

    private async Task<List<IBar>?> GetReferenceDataAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        try
        {
            // Try to get SPY data as market reference
            var spyData = await _marketDataService.GetStockBarsAsync("SPY", startDate, endDate);
            if (spyData.Items.Any())
            {
                return spyData.Items.ToList();
            }

            // Fallback to cached SPY data
            var cachedSpy = await _cacheService.GetBarsAsync("SPY", startDate, endDate);
            if (cachedSpy.Any())
            {
                return cachedSpy.ToList();
            }

            _logger.LogWarning("No SPY reference data available for synthetic generation");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get reference data for synthetic generation");
            return null;
        }
    }

    private async Task<CorrelationData> GetHistoricalCorrelationAsync(string symbol, CancellationToken cancellationToken)
    {
        try
        {
            // Try to get recent historical data for the symbol to calculate correlation
            var endDate = DateTime.UtcNow.Date.AddDays(-1);
            var startDate = endDate.AddDays(-30); // Last 30 days

            var symbolData = await _cacheService.GetBarsAsync(symbol, startDate, endDate);
            var spyData = await _cacheService.GetBarsAsync("SPY", startDate, endDate);

            if (symbolData.Any() && spyData.Any())
            {
                var correlation = CalculateCorrelation(symbolData, spyData);
                var beta = CalculateBeta(symbolData, spyData);
                var avgVolatility = CalculateAverageVolatility(symbolData);

                return new CorrelationData
                {
                    Correlation = correlation,
                    Beta = beta,
                    AverageVolatility = avgVolatility,
                    HasHistoricalData = true
                };
            }

            // Fallback to sector-based estimates
            return GetSectorBasedCorrelation(symbol);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to calculate historical correlation for {Symbol}, using defaults", symbol);
            return GetDefaultCorrelation();
        }
    }

    private List<IBar> GenerateSyntheticBars(
        string symbol, 
        List<IBar> referenceData, 
        CorrelationData correlation,
        DateTime startDate,
        DateTime endDate)
    {
        var syntheticBars = new List<IBar>();
        var random = new Random(_config.RandomSeed ?? symbol.GetHashCode());

        // Get the last known price for the symbol (if available)
        var lastKnownPrice = GetLastKnownPrice(symbol);
        var currentPrice = lastKnownPrice ?? _config.DefaultStartPrice;

        foreach (var referenceBar in referenceData.Where(b => b.TimeUtc >= startDate && b.TimeUtc <= endDate))
        {
            // Calculate reference return
            var referenceReturn = referenceBar.Close / referenceBar.Open - 1.0m;

            // Apply correlation and beta to generate symbol return
            var correlatedReturn = (decimal)correlation.Beta * referenceReturn;
            
            // Add some noise based on volatility
            var noise = (decimal)(random.NextGaussian() * correlation.AverageVolatility * 0.1);
            var symbolReturn = correlatedReturn + noise;

            // Calculate new price
            var newPrice = currentPrice * (1 + symbolReturn);
            
            // Generate OHLC based on the new price and volatility
            var (open, high, low, close) = GenerateOHLC(currentPrice, newPrice, correlation.AverageVolatility, random);

            // Generate volume based on reference volume and symbol characteristics
            var volume = GenerateVolume((ulong)Math.Max(1, referenceBar.Volume), correlation, random);

            var syntheticBar = new SyntheticBar
            {
                Symbol = symbol,
                TimeUtc = referenceBar.TimeUtc,
                Open = open,
                High = high,
                Low = low,
                Close = close,
                Volume = volume,
                Vwap = (high + low + close) / 3, // Simple VWAP approximation
                TradeCount = (ulong)(volume / 100) // Estimate trade count
            };

            syntheticBars.Add(syntheticBar);
            currentPrice = close;
        }

        return syntheticBars;
    }

    private (decimal open, decimal high, decimal low, decimal close) GenerateOHLC(
        decimal startPrice, 
        decimal endPrice, 
        double volatility, 
        Random random)
    {
        var open = startPrice;
        var close = endPrice;

        // Generate high and low based on volatility
        var dailyRange = (decimal)(volatility * 2.0); // Approximate daily range
        var midPrice = (open + close) / 2;

        var high = Math.Max(open, close) + midPrice * dailyRange * (decimal)random.NextDouble();
        var low = Math.Min(open, close) - midPrice * dailyRange * (decimal)random.NextDouble();

        // Ensure logical OHLC relationships
        high = Math.Max(high, Math.Max(open, close));
        low = Math.Min(low, Math.Min(open, close));

        return (open, high, low, close);
    }

    private ulong GenerateVolume(ulong referenceVolume, CorrelationData correlation, Random random)
    {
        // Base volume on reference volume with some variation
        var baseVolume = referenceVolume * _config.VolumeMultiplier;
        var variation = (decimal)(random.NextGaussian() * 0.3); // 30% standard deviation
        var adjustedVolume = baseVolume * (1 + variation);

        return (ulong)Math.Max(1000, adjustedVolume); // Minimum 1000 shares
    }

    private decimal? GetLastKnownPrice(string symbol)
    {
        try
        {
            // Try to get the most recent cached price
            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.AddDays(-7); // Look back 7 days

            var recentBars = _cacheService.GetBarsAsync(symbol, startDate, endDate).Result;
            return recentBars.OrderByDescending(b => b.TimeUtc).FirstOrDefault()?.Close;
        }
        catch
        {
            return null;
        }
    }

    private static double CalculateCorrelation(IEnumerable<IBar> symbolData, IEnumerable<IBar> spyData)
    {
        var symbolReturns = symbolData.Select(b => (double)(b.Close / b.Open - 1)).ToArray();
        var spyReturns = spyData.Select(b => (double)(b.Close / b.Open - 1)).ToArray();

        if (symbolReturns.Length != spyReturns.Length || symbolReturns.Length < 2)
            return 0.5; // Default correlation

        var correlation = CalculatePearsonCorrelation(symbolReturns, spyReturns);
        return Math.Max(-1.0, Math.Min(1.0, correlation)); // Clamp to [-1, 1]
    }

    private static double CalculateBeta(IEnumerable<IBar> symbolData, IEnumerable<IBar> spyData)
    {
        var symbolReturns = symbolData.Select(b => (double)(b.Close / b.Open - 1)).ToArray();
        var spyReturns = spyData.Select(b => (double)(b.Close / b.Open - 1)).ToArray();

        if (symbolReturns.Length != spyReturns.Length || symbolReturns.Length < 2)
            return 1.0; // Default beta

        var covariance = CalculateCovariance(symbolReturns, spyReturns);
        var spyVariance = CalculateVariance(spyReturns);

        return spyVariance > 0 ? covariance / spyVariance : 1.0;
    }

    private static double CalculateAverageVolatility(IEnumerable<IBar> symbolData)
    {
        var returns = symbolData.Select(b => (double)(b.Close / b.Open - 1)).ToArray();
        return returns.Length > 1 ? CalculateStandardDeviation(returns) : 0.02; // Default 2% volatility
    }

    private static double CalculatePearsonCorrelation(double[] x, double[] y)
    {
        var n = x.Length;
        var sumX = x.Sum();
        var sumY = y.Sum();
        var sumXY = x.Zip(y, (a, b) => a * b).Sum();
        var sumX2 = x.Sum(a => a * a);
        var sumY2 = y.Sum(b => b * b);

        var numerator = n * sumXY - sumX * sumY;
        var denominator = Math.Sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));

        return denominator > 0 ? numerator / denominator : 0;
    }

    private static double CalculateCovariance(double[] x, double[] y)
    {
        var meanX = x.Average();
        var meanY = y.Average();
        return x.Zip(y, (a, b) => (a - meanX) * (b - meanY)).Average();
    }

    private static double CalculateVariance(double[] values)
    {
        var mean = values.Average();
        return values.Select(v => Math.Pow(v - mean, 2)).Average();
    }

    private static double CalculateStandardDeviation(double[] values)
    {
        return Math.Sqrt(CalculateVariance(values));
    }

    private CorrelationData GetSectorBasedCorrelation(string symbol)
    {
        // Simple sector-based correlation estimates
        // In a real implementation, this would use a sector classification service
        var sectorCorrelations = new Dictionary<string, CorrelationData>
        {
            ["TECH"] = new() { Correlation = 0.7, Beta = 1.2, AverageVolatility = 0.025 },
            ["FINANCE"] = new() { Correlation = 0.8, Beta = 1.1, AverageVolatility = 0.020 },
            ["HEALTHCARE"] = new() { Correlation = 0.6, Beta = 0.9, AverageVolatility = 0.018 },
            ["ENERGY"] = new() { Correlation = 0.5, Beta = 1.3, AverageVolatility = 0.030 },
            ["DEFAULT"] = new() { Correlation = 0.6, Beta = 1.0, AverageVolatility = 0.020 }
        };

        // Simple heuristic based on symbol
        var sector = symbol switch
        {
            var s when s.StartsWith("AAPL") || s.StartsWith("MSFT") || s.StartsWith("GOOGL") => "TECH",
            var s when s.StartsWith("JPM") || s.StartsWith("BAC") || s.StartsWith("WFC") => "FINANCE",
            var s when s.StartsWith("JNJ") || s.StartsWith("PFE") || s.StartsWith("UNH") => "HEALTHCARE",
            var s when s.StartsWith("XOM") || s.StartsWith("CVX") || s.StartsWith("COP") => "ENERGY",
            _ => "DEFAULT"
        };

        return sectorCorrelations[sector];
    }

    private static CorrelationData GetDefaultCorrelation()
    {
        return new CorrelationData
        {
            Correlation = 0.6,
            Beta = 1.0,
            AverageVolatility = 0.020,
            HasHistoricalData = false
        };
    }
}

/// <summary>
/// Correlation data for synthetic generation
/// </summary>
public sealed class CorrelationData
{
    public double Correlation { get; set; }
    public double Beta { get; set; }
    public double AverageVolatility { get; set; }
    public bool HasHistoricalData { get; set; }
}

/// <summary>
/// Extension methods for random number generation
/// </summary>
public static class RandomExtensions
{
    public static double NextGaussian(this Random random)
    {
        // Box-Muller transform for Gaussian distribution
        var u1 = 1.0 - random.NextDouble();
        var u2 = 1.0 - random.NextDouble();
        return Math.Sqrt(-2.0 * Math.Log(u1)) * Math.Sin(2.0 * Math.PI * u2);
    }
}

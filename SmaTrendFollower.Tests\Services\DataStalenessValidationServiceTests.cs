using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Services;
using System;
using System.Threading.Tasks;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

public class DataStalenessValidationServiceTests
{
    private readonly DataStalenessValidationService _service;
    private readonly DataStalenessConfiguration _config;

    public DataStalenessValidationServiceTests()
    {
        _config = new DataStalenessConfiguration
        {
            EnableStrictStalenessChecks = true,
            RejectStaleData = true,
            LogStalenessWarnings = true,
            MarketHours = new MarketHoursStalenessThresholds
            {
                HistoricalBars = TimeSpan.FromMinutes(18),
                RealTimeQuotes = TimeSpan.FromMinutes(2),
                IndexData = TimeSpan.FromMinutes(15),
                VixData = TimeSpan.FromMinutes(15)
            },
            AfterHours = new AfterHoursStalenessThresholds
            {
                HistoricalBars = TimeSpan.FromHours(8),
                RealTimeQuotes = TimeSpan.FromHours(8),
                IndexData = TimeSpan.FromHours(8),
                VixData = TimeSpan.FromHours(8)
            }
        };

        var options = Options.Create(_config);
        var logger = new LoggerFactory().CreateLogger<DataStalenessValidationService>();
        _service = new DataStalenessValidationService(options, logger);
    }

    [Fact]
    public async Task ValidateDataFreshness_FreshData_ReturnsNotStale()
    {
        // Arrange
        var freshTimestamp = DateTime.UtcNow.AddMinutes(-5); // 5 minutes old
        var dataType = DataType.HistoricalBars;
        var dataSource = "TestSource";

        // Act
        var result = await _service.ValidateDataFreshnessAsync(freshTimestamp, dataType, dataSource);

        // Assert
        result.IsStale.Should().BeFalse();
        result.ShouldReject.Should().BeFalse();
        result.DataAge.Should().BeCloseTo(TimeSpan.FromMinutes(5), TimeSpan.FromSeconds(10));
        result.DataType.Should().Be(dataType);
        result.DataSource.Should().Be(dataSource);
    }

    [Fact]
    public async Task ValidateDataFreshness_StaleDataDuringMarketHours_ReturnsStaleAndShouldReject()
    {
        // Arrange
        var staleTimestamp = DateTime.UtcNow.AddMinutes(-30); // 30 minutes old (exceeds 18-minute threshold)
        var dataType = DataType.HistoricalBars;
        var dataSource = "TestSource";

        // Act
        var result = await _service.ValidateDataFreshnessAsync(staleTimestamp, dataType, dataSource);

        // Assert
        result.IsStale.Should().BeTrue();
        result.ShouldReject.Should().BeTrue();
        result.DataAge.Should().BeCloseTo(TimeSpan.FromMinutes(30), TimeSpan.FromSeconds(10));
    }

    [Fact]
    public async Task ValidateDataFreshness_StrictChecksDisabled_ReturnsNotStale()
    {
        // Arrange
        _config.EnableStrictStalenessChecks = false;
        var staleTimestamp = DateTime.UtcNow.AddHours(-2); // Very old data
        var dataType = DataType.HistoricalBars;
        var dataSource = "TestSource";

        // Act
        var result = await _service.ValidateDataFreshnessAsync(staleTimestamp, dataType, dataSource);

        // Assert
        result.IsStale.Should().BeFalse("strict checks are disabled");
        result.ShouldReject.Should().BeFalse();
    }

    [Fact]
    public async Task ValidateDataFreshness_RejectStaleDataDisabled_ReturnsStaleButNoReject()
    {
        // Arrange
        _config.RejectStaleData = false;
        var staleTimestamp = DateTime.UtcNow.AddMinutes(-30); // 30 minutes old
        var dataType = DataType.HistoricalBars;
        var dataSource = "TestSource";

        // Act
        var result = await _service.ValidateDataFreshnessAsync(staleTimestamp, dataType, dataSource);

        // Assert
        result.IsStale.Should().BeTrue();
        result.ShouldReject.Should().BeFalse("reject stale data is disabled");
    }

    [Theory]
    [InlineData(DataType.HistoricalBars, 18)]
    [InlineData(DataType.RealTimeQuotes, 2)]
    [InlineData(DataType.IndexData, 15)]
    [InlineData(DataType.VixData, 15)]
    [InlineData(DataType.UniverseData, 240)] // 4 hours
    [InlineData(DataType.SignalData, 30)]
    [InlineData(DataType.IndicatorData, 20)]
    public void GetCurrentThreshold_MarketHours_ReturnsCorrectThreshold(DataType dataType, int expectedMinutes)
    {
        // Act
        var threshold = _service.GetCurrentThreshold(dataType);

        // Assert
        threshold.Should().Be(TimeSpan.FromMinutes(expectedMinutes));
    }

    [Fact]
    public void IsMarketHours_WeekendTime_ReturnsFalse()
    {
        // Note: This test may be flaky depending on when it runs
        // In a real implementation, you'd want to inject a time provider for testing
        
        // Act
        var isMarketHours = _service.IsMarketHours();

        // Assert
        // We can't assert a specific value since it depends on when the test runs
        // But we can verify the method doesn't throw
        isMarketHours.Should().BeOfType<bool>();
    }

    [Fact]
    public void DataStalenessExtensions_GetThreshold_ReturnsCorrectThresholdBasedOnMarketHours()
    {
        // Arrange
        var config = new DataStalenessConfiguration();

        // Act - Market hours
        var marketHoursThreshold = config.GetThreshold(DataType.HistoricalBars, isMarketHours: true);
        
        // Act - After hours
        var afterHoursThreshold = config.GetThreshold(DataType.HistoricalBars, isMarketHours: false);

        // Assert
        marketHoursThreshold.Should().Be(TimeSpan.FromMinutes(18));
        afterHoursThreshold.Should().Be(TimeSpan.FromHours(8));
    }

    [Fact]
    public void DataStalenessExtensions_IsDataStale_ReturnsTrueForStaleData()
    {
        // Arrange
        var config = new DataStalenessConfiguration();
        var staleTimestamp = DateTime.UtcNow.AddMinutes(-30); // 30 minutes old

        // Act
        var isStale = config.IsDataStale(staleTimestamp, DataType.HistoricalBars, isMarketHours: true);

        // Assert
        isStale.Should().BeTrue("data is older than 18-minute threshold");
    }

    [Fact]
    public void DataStalenessExtensions_IsDataStale_ReturnsFalseForFreshData()
    {
        // Arrange
        var config = new DataStalenessConfiguration();
        var freshTimestamp = DateTime.UtcNow.AddMinutes(-5); // 5 minutes old

        // Act
        var isStale = config.IsDataStale(freshTimestamp, DataType.HistoricalBars, isMarketHours: true);

        // Assert
        isStale.Should().BeFalse("data is within 18-minute threshold");
    }

    [Fact]
    public async Task ValidateAndReturnAsync_FreshData_ReturnsData()
    {
        // Arrange
        var testData = "test data";
        var freshTimestamp = DateTime.UtcNow.AddMinutes(-5);

        // Act
        var result = await _service.ValidateAndReturnAsync(testData, freshTimestamp, DataType.HistoricalBars, "TestSource");

        // Assert
        result.Should().Be(testData);
    }

    [Fact]
    public async Task ValidateAndReturnAsync_StaleData_ThrowsDataStalenessException()
    {
        // Arrange
        var testData = "test data";
        var staleTimestamp = DateTime.UtcNow.AddMinutes(-30);

        // Act & Assert
        var act = async () => await _service.ValidateAndReturnAsync(testData, staleTimestamp, DataType.HistoricalBars, "TestSource");
        
        await act.Should().ThrowAsync<DataStalenessException>()
            .WithMessage("*TestSource*stale*");
    }

    [Fact]
    public void DataStalenessException_ContainsValidationResult()
    {
        // Arrange
        var validationResult = new DataStalenessValidationResult
        {
            IsStale = true,
            DataAge = TimeSpan.FromMinutes(30),
            Threshold = TimeSpan.FromMinutes(18),
            DataType = DataType.HistoricalBars,
            DataSource = "TestSource"
        };

        // Act
        var exception = new DataStalenessException("Test message", validationResult);

        // Assert
        exception.ValidationResult.Should().Be(validationResult);
        exception.Message.Should().Be("Test message");
    }
}

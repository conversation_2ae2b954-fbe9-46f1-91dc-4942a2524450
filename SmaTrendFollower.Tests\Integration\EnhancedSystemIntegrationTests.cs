using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using FluentAssertions;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// End-to-end integration tests for the enhanced trading system
/// Validates that all enhanced components work together correctly
/// </summary>
public sealed class EnhancedSystemIntegrationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;

    public EnhancedSystemIntegrationTests()
    {
        // Create test configuration
        _configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                // Enhanced Services Configuration
                ["EnhancedServices:EnableEnhancedDataRetrieval"] = "true",
                ["EnhancedServices:EnableAdaptiveRateLimit"] = "true",
                ["EnhancedServices:EnableAdaptiveSignalGeneration"] = "true",
                ["EnhancedServices:EnableSyntheticData"] = "false", // Disable for testing
                
                // Data Retrieval Configuration
                ["EnhancedDataRetrieval:MaxConcurrentRequests"] = "5",
                ["EnhancedDataRetrieval:PrimaryApiTimeout"] = "00:00:30",
                ["EnhancedDataRetrieval:BatchTimeout"] = "00:01:00",
                
                // Rate Limiting Configuration
                ["AdaptiveRateLimit:Providers:Test:InitialLimit"] = "10",
                ["AdaptiveRateLimit:Providers:Test:MinLimit"] = "5",
                ["AdaptiveRateLimit:Providers:Test:MaxLimit"] = "20",
                
                // Signal Generation Configuration
                ["AdaptiveSignal:MaxSymbolsToProcess"] = "50",
                ["AdaptiveSignal:MaxConcurrentSymbols"] = "5",
                ["AdaptiveSignal:MinimumBarsRequired"] = "10",
                
                // Robust Signal Configuration
                ["RobustSignal:MinimumAcceptableSignals"] = "2",
                ["RobustSignal:EnableFallbackGeneration"] = "true",
                ["RobustSignal:EnableEmergencyGeneration"] = "true"
            })
            .Build();

        // Build service provider with enhanced services
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        
        // Add enhanced services for testing
        services.AddEnhancedServicesForTesting(_configuration);
        
        _serviceProvider = services.BuildServiceProvider();
    }

    [Fact]
    public async Task EnhancedSystem_ServiceRegistration_AllServicesResolved()
    {
        // Act & Assert - Verify all enhanced services can be resolved
        var enhancedDataRetrieval = _serviceProvider.GetService<IEnhancedDataRetrievalService>();
        enhancedDataRetrieval.Should().NotBeNull();

        var adaptiveRateLimit = _serviceProvider.GetService<IAdaptiveRateLimitingService>();
        adaptiveRateLimit.Should().NotBeNull();

        var adaptiveSignalGenerator = _serviceProvider.GetService<IAdaptiveSignalGenerator>();
        adaptiveSignalGenerator.Should().NotBeNull();

        var robustSignalService = _serviceProvider.GetService<IRobustSignalGenerationService>();
        robustSignalService.Should().NotBeNull();

        var migrationService = _serviceProvider.GetService<IEnhancedServicesMigrationService>();
        migrationService.Should().NotBeNull();

        var stalenessService = _serviceProvider.GetService<IFlexibleDataStalenessService>();
        stalenessService.Should().NotBeNull();
    }

    [Fact]
    public async Task EnhancedSystem_Configuration_LoadedCorrectly()
    {
        // Arrange
        var enhancedOptions = _serviceProvider.GetRequiredService<EnhancedServicesOptions>();

        // Assert
        enhancedOptions.EnableEnhancedDataRetrieval.Should().BeTrue();
        enhancedOptions.EnableAdaptiveRateLimit.Should().BeTrue();
        enhancedOptions.EnableAdaptiveSignalGeneration.Should().BeTrue();
        enhancedOptions.EnableSyntheticData.Should().BeFalse(); // Disabled for testing
    }

    [Fact]
    public async Task MigrationService_HealthCheck_ReturnsValidStatus()
    {
        // Arrange
        var migrationService = _serviceProvider.GetRequiredService<IEnhancedServicesMigrationService>();

        // Act
        var healthResult = await migrationService.PerformHealthCheckAsync();

        // Assert
        healthResult.Should().NotBeNull();
        healthResult.OverallHealth.Should().BeOneOf(HealthStatus.Healthy, HealthStatus.Degraded);
        healthResult.ServiceChecks.Should().NotBeEmpty();
        healthResult.ServiceChecks.Should().ContainKeys("EnhancedDataRetrieval", "AdaptiveRateLimit", "AdaptiveSignalGeneration");
    }

    [Fact]
    public async Task MigrationService_FeatureFlags_WorkCorrectly()
    {
        // Arrange
        var migrationService = _serviceProvider.GetRequiredService<IEnhancedServicesMigrationService>();

        // Act & Assert - Initial state
        migrationService.ShouldUseEnhancedDataRetrieval().Should().BeTrue();
        migrationService.ShouldUseAdaptiveRateLimit().Should().BeTrue();
        migrationService.ShouldUseAdaptiveSignalGeneration().Should().BeTrue();

        // Act - Disable a service
        await migrationService.DisableServiceAsync("EnhancedDataRetrieval", "Test rollback");

        // Assert - Service should be disabled
        migrationService.ShouldUseEnhancedDataRetrieval().Should().BeFalse();

        // Act - Re-enable the service
        await migrationService.EnableServiceAsync("EnhancedDataRetrieval", "Test re-enable");

        // Assert - Service should be enabled again
        migrationService.ShouldUseEnhancedDataRetrieval().Should().BeTrue();
    }

    [Fact]
    public async Task MigrationService_MigrationReport_ProvidesDetailedStatus()
    {
        // Arrange
        var migrationService = _serviceProvider.GetRequiredService<IEnhancedServicesMigrationService>();

        // Act
        var report = migrationService.GetMigrationReport();

        // Assert
        report.Should().NotBeNull();
        report.GeneratedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
        report.OverallStatus.Should().NotBeNullOrEmpty();
        report.ServiceStatuses.Should().NotBeEmpty();
        report.ConfigurationStatus.Should().NotBeNull();
        
        // Verify service statuses
        report.ServiceStatuses.Should().ContainKeys("EnhancedDataRetrieval", "AdaptiveRateLimit", "AdaptiveSignalGeneration");
        
        foreach (var status in report.ServiceStatuses.Values)
        {
            status.ServiceName.Should().NotBeNullOrEmpty();
            status.ChangeHistory.Should().NotBeEmpty();
            status.LastChangeTime.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(5));
        }
    }

    [Fact]
    public async Task StalenessService_PolicyManagement_WorksCorrectly()
    {
        // Arrange
        var stalenessService = _serviceProvider.GetRequiredService<IFlexibleDataStalenessService>();

        // Act & Assert - Initial status
        var initialStatus = stalenessService.GetCurrentStatus();
        initialStatus.Should().NotBeNull();
        initialStatus.CurrentPolicy.Should().Be(StalenessPolicy.Standard);
        initialStatus.EmergencyModeActive.Should().BeFalse();

        // Act - Activate emergency mode
        await stalenessService.ActivateEmergencyModeAsync("Integration test", TimeSpan.FromMinutes(1));

        // Assert - Emergency mode should be active
        var emergencyStatus = stalenessService.GetCurrentStatus();
        emergencyStatus.EmergencyModeActive.Should().BeTrue();
        emergencyStatus.CurrentPolicy.Should().Be(StalenessPolicy.Emergency);

        // Act - Deactivate emergency mode
        await stalenessService.DeactivateEmergencyModeAsync("Test completed");

        // Assert - Emergency mode should be deactivated
        var finalStatus = stalenessService.GetCurrentStatus();
        finalStatus.EmergencyModeActive.Should().BeFalse();
        finalStatus.CurrentPolicy.Should().Be(StalenessPolicy.Standard);
    }

    [Fact]
    public async Task StalenessService_DataValidation_HandlesVariousStalenessLevels()
    {
        // Arrange
        var stalenessService = _serviceProvider.GetRequiredService<IFlexibleDataStalenessService>();

        // Test data with different staleness levels
        var testCases = new[]
        {
            new { Age = TimeSpan.FromMinutes(5), ExpectedLevel = StalenessLevel.Fresh },
            new { Age = TimeSpan.FromMinutes(30), ExpectedLevel = StalenessLevel.Acceptable },
            new { Age = TimeSpan.FromHours(2), ExpectedLevel = StalenessLevel.Stale },
            new { Age = TimeSpan.FromHours(12), ExpectedLevel = StalenessLevel.VeryStale },
            new { Age = TimeSpan.FromDays(2), ExpectedLevel = StalenessLevel.Ancient }
        };

        foreach (var testCase in testCases)
        {
            // Act
            var dataTimestamp = DateTime.UtcNow - testCase.Age;
            var result = await stalenessService.ValidateDataFreshnessAsync(
                dataTimestamp, 
                DataType.HistoricalBars, 
                "TestSource");

            // Assert
            result.Should().NotBeNull();
            result.StalenessLevel.Should().Be(testCase.ExpectedLevel);
            result.DataAge.Should().BeCloseTo(testCase.Age, TimeSpan.FromSeconds(5));
            result.QualityScore.Should().BeInRange(0.0, 1.0);
            result.RecommendedAction.Should().NotBe(default(RecommendedAction));
        }
    }

    [Fact]
    public async Task RateLimitingService_BasicFunctionality_WorksCorrectly()
    {
        // Arrange
        var rateLimitService = _serviceProvider.GetRequiredService<IAdaptiveRateLimitingService>();

        // Act - Try to acquire permits
        var result1 = await rateLimitService.TryAcquireAsync("Test", "operation1");
        var result2 = await rateLimitService.TryAcquireAsync("Test", "operation2");

        // Assert
        result1.Should().NotBeNull();
        result2.Should().NotBeNull();
        
        // Release permits
        rateLimitService.Release("Test", "operation1", true, TimeSpan.FromMilliseconds(100));
        rateLimitService.Release("Test", "operation2", true, TimeSpan.FromMilliseconds(150));

        // Get statistics
        var stats = rateLimitService.GetStats("Test");
        stats.Should().NotBeNull();
        stats.Provider.Should().Be("Test");
        stats.TotalRequests.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task IntegratedSystem_EndToEndFlow_WorksWithoutErrors()
    {
        // This test validates that the entire enhanced system can be initialized
        // and basic operations work without throwing exceptions
        
        // Arrange
        var migrationService = _serviceProvider.GetRequiredService<IEnhancedServicesMigrationService>();
        var stalenessService = _serviceProvider.GetRequiredService<IFlexibleDataStalenessService>();
        var rateLimitService = _serviceProvider.GetRequiredService<IAdaptiveRateLimitingService>();

        // Act & Assert - No exceptions should be thrown
        var healthCheck = await migrationService.PerformHealthCheckAsync();
        healthCheck.Should().NotBeNull();

        var migrationReport = migrationService.GetMigrationReport();
        migrationReport.Should().NotBeNull();

        var stalenessStatus = stalenessService.GetCurrentStatus();
        stalenessStatus.Should().NotBeNull();

        var rateLimitStats = rateLimitService.GetStats("NonExistentProvider");
        rateLimitStats.Should().NotBeNull();

        // Verify the system is in a consistent state
        healthCheck.OverallHealth.Should().NotBe(HealthStatus.Unhealthy);
        migrationReport.OverallStatus.Should().NotBeNullOrEmpty();
        stalenessStatus.CurrentPolicy.Should().NotBe(default(StalenessPolicy));
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}

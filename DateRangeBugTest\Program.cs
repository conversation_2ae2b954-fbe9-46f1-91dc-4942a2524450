using System;

namespace SmaTrendFollower.Tests
{
    /// <summary>
    /// Test to verify the date range bug fix in StockBarCacheService.GetMissingDateRangeAsync
    /// </summary>
    public class DateRangeBugTest
    {
        public static void TestDateRangeLogic()
        {
            Console.WriteLine("Testing Date Range Logic Fix");
            Console.WriteLine("============================");

            // Test Case 1: Need newer data
            TestCase1_NeedNewerData();
            
            // Test Case 2: Need older data  
            TestCase2_NeedOlderData();
            
            // Test Case 3: Cache covers entire range
            TestCase3_CacheCoversRange();
            
            // Test Case 4: No cached data
            TestCase4_NoCachedData();

            Console.WriteLine("\n✅ All date range logic tests passed!");
        }

        private static void TestCase1_NeedNewerData()
        {
            Console.WriteLine("\nTest Case 1: Need newer data");
            
            var requestedStartDate = new DateTime(2025, 1, 1);
            var requestedEndDate = new DateTime(2025, 1, 10);
            var earliestCachedDate = new DateTime(2025, 1, 5);
            var latestCachedDate = new DateTime(2025, 1, 8);
            
            // Simulate the fixed logic
            if (requestedEndDate > latestCachedDate)
            {
                var fetchStartDate = Math.Max(requestedStartDate.Ticks, latestCachedDate.AddDays(1).Ticks);
                var fetchStart = new DateTime(fetchStartDate);
                var fetchEnd = requestedEndDate;
                
                Console.WriteLine($"  Requested: {requestedStartDate:yyyy-MM-dd} to {requestedEndDate:yyyy-MM-dd}");
                Console.WriteLine($"  Cached: {earliestCachedDate:yyyy-MM-dd} to {latestCachedDate:yyyy-MM-dd}");
                Console.WriteLine($"  Need to fetch: {fetchStart:yyyy-MM-dd} to {fetchEnd:yyyy-MM-dd}");
                
                if (fetchStart > fetchEnd)
                {
                    throw new Exception("❌ Invalid date range: startDate > endDate");
                }
                Console.WriteLine("  ✅ Valid date range");
            }
        }

        private static void TestCase2_NeedOlderData()
        {
            Console.WriteLine("\nTest Case 2: Need older data");
            
            var requestedStartDate = new DateTime(2025, 1, 1);
            var requestedEndDate = new DateTime(2025, 1, 10);
            var earliestCachedDate = new DateTime(2025, 1, 5);
            var latestCachedDate = new DateTime(2025, 1, 15);
            
            // Simulate the fixed logic
            if (requestedStartDate < earliestCachedDate)
            {
                var fetchEndDate = Math.Min(requestedEndDate.Ticks, earliestCachedDate.AddDays(-1).Ticks);
                var fetchEnd = new DateTime(fetchEndDate);
                var fetchStart = requestedStartDate;
                
                Console.WriteLine($"  Requested: {requestedStartDate:yyyy-MM-dd} to {requestedEndDate:yyyy-MM-dd}");
                Console.WriteLine($"  Cached: {earliestCachedDate:yyyy-MM-dd} to {latestCachedDate:yyyy-MM-dd}");
                Console.WriteLine($"  Need to fetch: {fetchStart:yyyy-MM-dd} to {fetchEnd:yyyy-MM-dd}");
                
                if (fetchStart >= fetchEnd)
                {
                    Console.WriteLine("  ℹ️ No gap to fill for older data");
                }
                else
                {
                    Console.WriteLine("  ✅ Valid date range");
                }
            }
        }

        private static void TestCase3_CacheCoversRange()
        {
            Console.WriteLine("\nTest Case 3: Cache covers entire range");
            
            var requestedStartDate = new DateTime(2025, 1, 5);
            var requestedEndDate = new DateTime(2025, 1, 10);
            var earliestCachedDate = new DateTime(2025, 1, 1);
            var latestCachedDate = new DateTime(2025, 1, 15);
            
            Console.WriteLine($"  Requested: {requestedStartDate:yyyy-MM-dd} to {requestedEndDate:yyyy-MM-dd}");
            Console.WriteLine($"  Cached: {earliestCachedDate:yyyy-MM-dd} to {latestCachedDate:yyyy-MM-dd}");
            
            if (earliestCachedDate <= requestedStartDate && latestCachedDate >= requestedEndDate)
            {
                Console.WriteLine("  ✅ Cache covers entire range, no fetch needed");
            }
        }

        private static void TestCase4_NoCachedData()
        {
            Console.WriteLine("\nTest Case 4: No cached data");
            
            var requestedStartDate = new DateTime(2025, 1, 1);
            var requestedEndDate = new DateTime(2025, 1, 10);
            
            Console.WriteLine($"  Requested: {requestedStartDate:yyyy-MM-dd} to {requestedEndDate:yyyy-MM-dd}");
            Console.WriteLine($"  Cached: None");
            Console.WriteLine($"  Need to fetch: {requestedStartDate:yyyy-MM-dd} to {requestedEndDate:yyyy-MM-dd}");
            Console.WriteLine("  ✅ Valid date range");
        }

        /// <summary>
        /// Test the problematic scenario that was causing the bug
        /// </summary>
        public static void TestProblematicScenario()
        {
            Console.WriteLine("\nTesting Problematic Scenario (Original Bug)");
            Console.WriteLine("===========================================");
            
            var requestedStartDate = new DateTime(2025, 1, 1);
            var requestedEndDate = new DateTime(2025, 1, 10);
            var earliestCachedDate = new DateTime(2025, 1, 5);
            var latestCachedDate = new DateTime(2025, 1, 15);
            
            Console.WriteLine($"Requested: {requestedStartDate:yyyy-MM-dd} to {requestedEndDate:yyyy-MM-dd}");
            Console.WriteLine($"Cached: {earliestCachedDate:yyyy-MM-dd} to {latestCachedDate:yyyy-MM-dd}");
            
            // OLD BUGGY LOGIC (what was happening before):
            Console.WriteLine("\n❌ OLD BUGGY LOGIC:");
            var fetchStartDate_OLD = latestCachedDate.AddDays(1); // 2025-01-16
            var earliestMinusOne_OLD = earliestCachedDate.AddDays(-1); // 2025-01-04
            var fetchEndDate_OLD = requestedEndDate < earliestMinusOne_OLD ? requestedEndDate : earliestMinusOne_OLD; // 2025-01-04
            
            Console.WriteLine($"  fetchStartDate = {fetchStartDate_OLD:yyyy-MM-dd}");
            Console.WriteLine($"  fetchEndDate = {fetchEndDate_OLD:yyyy-MM-dd}");
            Console.WriteLine($"  Result: startDate ({fetchStartDate_OLD:yyyy-MM-dd}) > endDate ({fetchEndDate_OLD:yyyy-MM-dd}) = INVALID!");
            
            // NEW FIXED LOGIC:
            Console.WriteLine("\n✅ NEW FIXED LOGIC:");
            Console.WriteLine("  Since requestedStartDate < earliestCachedDate, we need older data:");
            var fetchStart_NEW = requestedStartDate; // 2025-01-01
            var fetchEnd_NEW = new DateTime(Math.Min(requestedEndDate.Ticks, earliestCachedDate.AddDays(-1).Ticks)); // 2025-01-04
            
            Console.WriteLine($"  fetchStartDate = {fetchStart_NEW:yyyy-MM-dd}");
            Console.WriteLine($"  fetchEndDate = {fetchEnd_NEW:yyyy-MM-dd}");
            Console.WriteLine($"  Result: startDate ({fetchStart_NEW:yyyy-MM-dd}) < endDate ({fetchEnd_NEW:yyyy-MM-dd}) = VALID!");
        }

        public static void Main(string[] args)
        {
            try
            {
                TestDateRangeLogic();
                TestProblematicScenario();
                Console.WriteLine("\n🎉 All tests completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ Test failed: {ex.Message}");
                Environment.Exit(1);
            }
        }
    }
}

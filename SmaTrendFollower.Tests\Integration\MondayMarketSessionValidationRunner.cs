using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.TestHelpers;
using Xunit;
using Xunit.Abstractions;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// Integration test runner for validating Monday market session logic against historical data
/// This test can run with real market data when available, or use mock data for CI/CD
/// </summary>
public class MondayMarketSessionValidationRunner : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MondayMarketSessionValidationRunner> _logger;

    public MondayMarketSessionValidationRunner(ITestOutputHelper output)
    {
        _output = output;
        _serviceProvider = CreateServiceProvider();
        _logger = _serviceProvider.GetRequiredService<ILogger<MondayMarketSessionValidationRunner>>();
    }

    /// <summary>
    /// Main validation test that runs comprehensive Monday market session checks
    /// </summary>
    [Fact]
    public async Task RunMondayMarketSessionValidation()
    {
        _output.WriteLine("🚀 Starting Monday Market Session Validation");
        _output.WriteLine($"Test started at: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");

        var validationResults = new List<ValidationResult>();

        try
        {
            // Test 1: Validate against known Monday holidays
            _output.WriteLine("\n📅 Test 1: Known Monday Holidays Validation");
            var holidayResults = await ValidateKnownMondayHolidays();
            validationResults.AddRange(holidayResults);

            // Test 2: Validate Monday market hours edge cases
            _output.WriteLine("\n⏰ Test 2: Monday Market Hours Edge Cases");
            var edgeCaseResults = await ValidateMondayMarketHoursEdgeCases();
            validationResults.AddRange(edgeCaseResults);

            // Test 3: Validate DST transitions on Mondays
            _output.WriteLine("\n🌅 Test 3: DST Transition Validation");
            var dstResults = await ValidateMondayDSTTransitions();
            validationResults.AddRange(dstResults);

            // Test 4: Historical data validation (if available)
            _output.WriteLine("\n📊 Test 4: Historical Data Validation");
            var historicalResults = await ValidateAgainstHistoricalData();
            validationResults.AddRange(historicalResults);

            // Analyze and report results
            await AnalyzeAndReportResults(validationResults);
        }
        catch (Exception ex)
        {
            _output.WriteLine($"❌ Validation failed with exception: {ex.Message}");
            _logger.LogError(ex, "Monday market session validation failed");
            throw;
        }
    }

    /// <summary>
    /// Validates market session logic against known Monday holidays
    /// </summary>
    private async Task<List<ValidationResult>> ValidateKnownMondayHolidays()
    {
        var results = new List<ValidationResult>();
        var marketCalendar = _serviceProvider.GetRequiredService<IMarketCalendarService>();

        // Known Monday holidays for 2024
        var mondayHolidays = new[]
        {
            new DateTime(2024, 1, 1),   // New Year's Day
            new DateTime(2024, 1, 15),  // MLK Day
            new DateTime(2024, 2, 19),  // Presidents Day
            new DateTime(2024, 5, 27),  // Memorial Day
            new DateTime(2024, 9, 2),   // Labor Day
        };

        foreach (var holiday in mondayHolidays)
        {
            var result = await ValidateMondaySession(holiday, false, $"Known holiday: {holiday:yyyy-MM-dd}");
            results.Add(result);
            
            _output.WriteLine($"  {holiday:yyyy-MM-dd} ({holiday.DayOfWeek}): {(result.IsValid ? "✅" : "❌")} {result.Description}");
        }

        // Test some regular Mondays
        var regularMondays = new[]
        {
            new DateTime(2024, 1, 8),   // Regular Monday
            new DateTime(2024, 3, 4),   // Regular Monday
            new DateTime(2024, 6, 3),   // Regular Monday
        };

        foreach (var monday in regularMondays)
        {
            var result = await ValidateMondaySession(monday, true, $"Regular trading Monday: {monday:yyyy-MM-dd}");
            results.Add(result);
            
            _output.WriteLine($"  {monday:yyyy-MM-dd} ({monday.DayOfWeek}): {(result.IsValid ? "✅" : "❌")} {result.Description}");
        }

        return results;
    }

    /// <summary>
    /// Validates Monday market hours edge cases (pre-market, post-market, etc.)
    /// </summary>
    private async Task<List<ValidationResult>> ValidateMondayMarketHoursEdgeCases()
    {
        var results = new List<ValidationResult>();
        var testMonday = new DateTime(2024, 3, 4); // Regular Monday

        var testCases = new[]
        {
            (testMonday.AddHours(8), false, "8:00 AM ET - Pre-market"),
            (testMonday.AddHours(9).AddMinutes(29), false, "9:29 AM ET - Just before open"),
            (testMonday.AddHours(9).AddMinutes(30), true, "9:30 AM ET - Market open"),
            (testMonday.AddHours(12), true, "12:00 PM ET - Mid-day"),
            (testMonday.AddHours(16), true, "4:00 PM ET - Market close"),
            (testMonday.AddHours(16).AddMinutes(1), false, "4:01 PM ET - After close"),
            (testMonday.AddHours(20), false, "8:00 PM ET - Evening"),
        };

        foreach (var (testTime, expectedCanTrade, description) in testCases)
        {
            var result = await ValidateSpecificTime(testTime, expectedCanTrade, description);
            results.Add(result);
            
            _output.WriteLine($"  {description}: {(result.IsValid ? "✅" : "❌")} Expected={expectedCanTrade}, Got={result.ActualCanTrade}");
        }

        return results;
    }

    /// <summary>
    /// Validates Monday sessions during DST transitions
    /// </summary>
    private async Task<List<ValidationResult>> ValidateMondayDSTTransitions()
    {
        var results = new List<ValidationResult>();

        // DST transition dates for 2024
        var dstTransitions = new[]
        {
            new DateTime(2024, 3, 11), // Spring forward (if Monday)
            new DateTime(2024, 11, 4), // Fall back (if Monday)
        };

        foreach (var transition in dstTransitions.Where(d => d.DayOfWeek == DayOfWeek.Monday))
        {
            // Test market hours during DST transition
            var marketOpenTime = transition.AddHours(9).AddMinutes(30); // Should be 9:30 AM ET
            var result = await ValidateSpecificTime(marketOpenTime, true, $"DST transition market open: {transition:yyyy-MM-dd}");
            results.Add(result);
            
            _output.WriteLine($"  DST {transition:yyyy-MM-dd}: {(result.IsValid ? "✅" : "❌")} Market open validation");
        }

        return results;
    }

    /// <summary>
    /// Validates against historical market data if available
    /// </summary>
    private async Task<List<ValidationResult>> ValidateAgainstHistoricalData()
    {
        var results = new List<ValidationResult>();
        
        try
        {
            var marketDataService = _serviceProvider.GetRequiredService<IMarketDataService>();
            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.AddDays(-30); // Last 30 days

            _output.WriteLine($"  Fetching SPY data from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");

            var spyBars = await marketDataService.GetStockBarsAsync("SPY", startDate, endDate);
            var tradingDays = spyBars.Items.Select(bar => bar.TimeUtc.Date).ToHashSet();

            _output.WriteLine($"  Found {tradingDays.Count} trading days in historical data");

            // Validate each Monday in the period
            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                if (date.DayOfWeek == DayOfWeek.Monday)
                {
                    var expectedTradingDay = tradingDays.Contains(date);
                    var result = await ValidateMondaySession(date, expectedTradingDay, 
                        $"Historical validation: {date:yyyy-MM-dd}");
                    results.Add(result);
                    
                    _output.WriteLine($"  {date:yyyy-MM-dd}: {(result.IsValid ? "✅" : "❌")} " +
                        $"Expected={expectedTradingDay}, Got={result.ActualCanTrade}");
                }
            }
        }
        catch (Exception ex)
        {
            _output.WriteLine($"  ⚠️ Historical data validation skipped: {ex.Message}");
            // Add a placeholder result indicating the test was skipped
            results.Add(new ValidationResult
            {
                TestName = "Historical Data Validation",
                IsValid = true, // Don't fail the test if historical data is unavailable
                Description = $"Skipped due to data unavailability: {ex.Message}",
                ActualCanTrade = false,
                ExpectedCanTrade = false
            });
        }

        return results;
    }

    /// <summary>
    /// Validates market session logic for a specific Monday
    /// </summary>
    private async Task<ValidationResult> ValidateMondaySession(DateTime mondayDate, bool expectedCanTrade, string description)
    {
        // Test at 2:00 PM ET (typical trading time)
        var testTime = mondayDate.Date.AddHours(19); // 2:00 PM ET = 7:00 PM UTC (EST)
        return await ValidateSpecificTime(testTime, expectedCanTrade, description);
    }

    /// <summary>
    /// Validates market session logic for a specific time
    /// </summary>
    private async Task<ValidationResult> ValidateSpecificTime(DateTime testTime, bool expectedCanTrade, string description)
    {
        var timeProvider = new TestTimeProvider(testTime);
        var guard = new MarketSessionGuard(timeProvider, _serviceProvider.GetRequiredService<ILogger<MarketSessionGuard>>());

        var actualCanTrade = await guard.CanTradeNowAsync();

        return new ValidationResult
        {
            TestName = "Monday Market Session",
            TestTime = testTime,
            ExpectedCanTrade = expectedCanTrade,
            ActualCanTrade = actualCanTrade,
            IsValid = expectedCanTrade == actualCanTrade,
            Description = description,
            Reason = guard.Reason
        };
    }

    /// <summary>
    /// Analyzes and reports the final validation results
    /// </summary>
    private async Task AnalyzeAndReportResults(List<ValidationResult> results)
    {
        var totalTests = results.Count;
        var passedTests = results.Count(r => r.IsValid);
        var failedTests = totalTests - passedTests;

        _output.WriteLine($"\n📊 Validation Summary:");
        _output.WriteLine($"  Total Tests: {totalTests}");
        _output.WriteLine($"  Passed: {passedTests} ✅");
        _output.WriteLine($"  Failed: {failedTests} ❌");
        _output.WriteLine($"  Success Rate: {(double)passedTests / totalTests:P1}");

        if (failedTests > 0)
        {
            _output.WriteLine($"\n❌ Failed Tests:");
            foreach (var failure in results.Where(r => !r.IsValid))
            {
                _output.WriteLine($"  - {failure.Description}");
                _output.WriteLine($"    Expected: {failure.ExpectedCanTrade}, Got: {failure.ActualCanTrade}");
                if (!string.IsNullOrEmpty(failure.Reason))
                    _output.WriteLine($"    Reason: {failure.Reason}");
            }
        }

        // Assert that all critical tests passed
        var criticalFailures = results.Where(r => !r.IsValid && !r.Description.Contains("Skipped")).ToList();
        criticalFailures.Should().BeEmpty("All critical Monday market session validations should pass");

        _output.WriteLine($"\n✅ Monday Market Session Validation Complete!");
    }

    private IServiceProvider CreateServiceProvider()
    {
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddXUnit(_output));

        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true)
            .AddEnvironmentVariables()
            .Build();
        services.AddSingleton<IConfiguration>(configuration);

        services.AddSingleton<IMarketCalendarService, MarketCalendarService>();
        services.AddSingleton<IMarketDataService, MockMarketDataService>();

        return services.BuildServiceProvider();
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}

/// <summary>
/// Result of a market session validation test
/// </summary>
public class ValidationResult
{
    public string TestName { get; set; } = string.Empty;
    public DateTime TestTime { get; set; }
    public bool ExpectedCanTrade { get; set; }
    public bool ActualCanTrade { get; set; }
    public bool IsValid { get; set; }
    public string Description { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
}

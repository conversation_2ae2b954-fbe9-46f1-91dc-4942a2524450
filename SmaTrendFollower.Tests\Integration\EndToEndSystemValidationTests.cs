using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using FluentAssertions;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using System.Diagnostics;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// Comprehensive end-to-end validation tests for the complete enhanced trading system
/// Validates the entire system works together under various scenarios
/// </summary>
public sealed class EndToEndSystemValidationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly ILogger<EndToEndSystemValidationTests> _logger;

    public EndToEndSystemValidationTests()
    {
        var configuration = CreateTestConfiguration();
        var services = new ServiceCollection();
        
        // Add logging
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        
        // Add the full trading system with enhanced services
        services.AddFullTradingSystem(configuration);
        
        _serviceProvider = services.BuildServiceProvider();
        _logger = _serviceProvider.GetRequiredService<ILogger<EndToEndSystemValidationTests>>();
    }

    [Fact]
    public async Task FullSystem_ServiceResolution_AllCriticalServicesAvailable()
    {
        _logger.LogInformation("🔍 Testing full system service resolution...");

        // Test core trading services
        var signalGenerator = _serviceProvider.GetService<ISignalGenerator>();
        signalGenerator.Should().NotBeNull("Signal generator is critical for trading");

        var marketDataService = _serviceProvider.GetService<IMarketDataService>();
        marketDataService.Should().NotBeNull("Market data service is critical for trading");

        // Test enhanced services
        var enhancedDataRetrieval = _serviceProvider.GetService<IEnhancedDataRetrievalService>();
        enhancedDataRetrieval.Should().NotBeNull("Enhanced data retrieval should be available");

        var migrationService = _serviceProvider.GetService<IEnhancedServicesMigrationService>();
        migrationService.Should().NotBeNull("Migration service should be available");

        var rateLimitService = _serviceProvider.GetService<IAdaptiveRateLimitingService>();
        rateLimitService.Should().NotBeNull("Rate limiting service should be available");

        // Test decorator pattern is working
        var enhancedSignalGenerator = _serviceProvider.GetService<IEnhancedSignalGenerator>();
        if (enhancedSignalGenerator != null)
        {
            _logger.LogInformation("✅ Enhanced signal generator decorator is active");
        }

        var enhancedMarketData = _serviceProvider.GetService<IEnhancedMarketDataService>();
        if (enhancedMarketData != null)
        {
            _logger.LogInformation("✅ Enhanced market data decorator is active");
        }

        _logger.LogInformation("✅ Full system service resolution test passed");
    }

    [Fact]
    public async Task FullSystem_Configuration_LoadedCorrectly()
    {
        _logger.LogInformation("⚙️ Testing full system configuration...");

        // Test enhanced services options
        var enhancedOptions = _serviceProvider.GetService<EnhancedServicesOptions>();
        if (enhancedOptions != null)
        {
            _logger.LogInformation("📋 Enhanced Services Config - Data: {Data}, Rate: {Rate}, Signal: {Signal}",
                enhancedOptions.EnableEnhancedDataRetrieval,
                enhancedOptions.EnableAdaptiveRateLimit,
                enhancedOptions.EnableAdaptiveSignalGeneration);
        }

        // Test that configuration sections are bound
        var dataRetrievalConfig = _serviceProvider.GetService<DataRetrievalConfiguration>();
        if (dataRetrievalConfig != null)
        {
            dataRetrievalConfig.MaxConcurrentRequests.Should().BeGreaterThan(0);
            dataRetrievalConfig.PrimaryApiTimeout.Should().BeGreaterThan(TimeSpan.Zero);
        }

        _logger.LogInformation("✅ Full system configuration test passed");
    }

    [Fact]
    public async Task FullSystem_EnhancedDataRetrieval_WorksWithFallbacks()
    {
        _logger.LogInformation("📊 Testing enhanced data retrieval with fallbacks...");

        var enhancedDataService = _serviceProvider.GetService<IEnhancedDataRetrievalService>();
        if (enhancedDataService == null)
        {
            _logger.LogWarning("⚠️ Enhanced data retrieval not available, skipping test");
            return;
        }

        try
        {
            // Test single symbol retrieval
            var symbol = "AAPL";
            var startDate = DateTime.UtcNow.AddDays(-30);
            var endDate = DateTime.UtcNow.AddDays(-1);

            using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(1));
            var result = await enhancedDataService.GetStockBarsAsync(symbol, startDate, endDate, cts.Token);

            result.Should().NotBeNull();
            _logger.LogInformation("📈 Data retrieval result - Success: {Success}, Source: {Source}, Quality: {Quality}, Attempts: {Attempts}",
                result.IsSuccess, result.FinalDataSource, result.DataQuality, result.Attempts.Count);

            // Test batch retrieval
            var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
            var batchResults = await enhancedDataService.GetStockBarsBatchAsync(symbols, startDate, endDate, cts.Token);

            batchResults.Should().NotBeNull();
            batchResults.Should().HaveCount(3);

            var successCount = batchResults.Values.Count(r => r.IsSuccess);
            _logger.LogInformation("📊 Batch retrieval - {Success}/{Total} successful", successCount, batchResults.Count);

            _logger.LogInformation("✅ Enhanced data retrieval test passed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Enhanced data retrieval test failed");
            throw;
        }
    }

    [Fact]
    public async Task FullSystem_SignalGeneration_WorksWithEnhancements()
    {
        _logger.LogInformation("📡 Testing signal generation with enhancements...");

        var signalGenerator = _serviceProvider.GetRequiredService<ISignalGenerator>();

        try
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(2));
            var signals = await signalGenerator.RunAsync(5, cts.Token);

            signals.Should().NotBeNull();
            _logger.LogInformation("🎯 Signal generation - Generated {Count} signals", signals.Count());

            // Test enhanced signal generator if available
            var enhancedSignalGenerator = _serviceProvider.GetService<IEnhancedSignalGenerator>();
            if (enhancedSignalGenerator != null)
            {
                var robustResult = await enhancedSignalGenerator.GenerateSignalsRobustlyAsync(3, cts.Token);
                
                _logger.LogInformation("🔧 Robust signal generation - Success: {Success}, Signals: {Count}, Methods: {Methods}",
                    robustResult.IsSuccess, robustResult.Signals.Count, string.Join(", ", robustResult.GenerationMethods));

                var errorStats = enhancedSignalGenerator.GetErrorStatistics();
                _logger.LogInformation("📊 Error statistics - Tracked: {Tracked}, Errors: {Errors}",
                    errorStats.TotalSymbolsTracked, errorStats.TotalErrors);
            }

            _logger.LogInformation("✅ Signal generation test passed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Signal generation test failed");
            throw;
        }
    }

    [Fact]
    public async Task FullSystem_RateLimiting_PreventsOverload()
    {
        _logger.LogInformation("🚦 Testing rate limiting prevents overload...");

        var rateLimitService = _serviceProvider.GetService<IAdaptiveRateLimitingService>();
        if (rateLimitService == null)
        {
            _logger.LogWarning("⚠️ Rate limiting service not available, skipping test");
            return;
        }

        try
        {
            var provider = "TestProvider";
            var acquiredPermits = new List<bool>();
            var stopwatch = Stopwatch.StartNew();

            // Try to acquire many permits quickly
            for (int i = 0; i < 20; i++)
            {
                var result = await rateLimitService.TryAcquireAsync(provider, $"operation_{i}");
                acquiredPermits.Add(result.IsSuccess);

                if (result.IsSuccess)
                {
                    // Simulate work and release
                    await Task.Delay(10);
                    rateLimitService.Release(provider, $"operation_{i}", true, TimeSpan.FromMilliseconds(10));
                }
            }

            stopwatch.Stop();

            var successCount = acquiredPermits.Count(p => p);
            var stats = rateLimitService.GetStats(provider);

            _logger.LogInformation("⏱️ Rate limiting test - {Success}/{Total} permits acquired in {Duration}ms",
                successCount, acquiredPermits.Count, stopwatch.ElapsedMilliseconds);

            _logger.LogInformation("📊 Rate limit stats - Limit: {Limit}, Available: {Available}, Total: {Total}",
                stats.CurrentLimit, stats.AvailablePermits, stats.TotalRequests);

            // Should not acquire all permits instantly (rate limiting should kick in)
            successCount.Should().BeLessThan(acquiredPermits.Count, "Rate limiting should prevent acquiring all permits");

            _logger.LogInformation("✅ Rate limiting test passed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Rate limiting test failed");
            throw;
        }
    }

    [Fact]
    public async Task FullSystem_EmergencyMode_ActivatesCorrectly()
    {
        _logger.LogInformation("🚨 Testing emergency mode activation...");

        var stalenessService = _serviceProvider.GetService<IFlexibleDataStalenessService>();
        if (stalenessService == null)
        {
            _logger.LogWarning("⚠️ Staleness service not available, skipping test");
            return;
        }

        try
        {
            // Check initial state
            var initialStatus = stalenessService.GetCurrentStatus();
            _logger.LogInformation("📊 Initial state - Policy: {Policy}, Emergency: {Emergency}",
                initialStatus.CurrentPolicy, initialStatus.EmergencyModeActive);

            // Activate emergency mode
            await stalenessService.ActivateEmergencyModeAsync("Integration test", TimeSpan.FromSeconds(30));

            var emergencyStatus = stalenessService.GetCurrentStatus();
            emergencyStatus.EmergencyModeActive.Should().BeTrue("Emergency mode should be active");
            emergencyStatus.CurrentPolicy.Should().Be(StalenessPolicy.Emergency);

            _logger.LogInformation("🚨 Emergency mode activated - Policy: {Policy}", emergencyStatus.CurrentPolicy);

            // Test data validation in emergency mode
            var veryStaleData = DateTime.UtcNow.AddDays(-1);
            var result = await stalenessService.ValidateDataFreshnessAsync(
                veryStaleData, DataType.HistoricalBars, "TestSource");

            _logger.LogInformation("📊 Stale data validation - Level: {Level}, Acceptable: {Acceptable}, Quality: {Quality:F2}",
                result.StalenessLevel, result.IsAcceptable, result.QualityScore);

            // In emergency mode, very stale data should be acceptable
            result.IsAcceptable.Should().BeTrue("Emergency mode should accept stale data");

            // Deactivate emergency mode
            await stalenessService.DeactivateEmergencyModeAsync("Test completed");

            var finalStatus = stalenessService.GetCurrentStatus();
            finalStatus.EmergencyModeActive.Should().BeFalse("Emergency mode should be deactivated");

            _logger.LogInformation("✅ Emergency mode test passed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Emergency mode test failed");
            throw;
        }
    }

    [Fact]
    public async Task FullSystem_MigrationManagement_WorksCorrectly()
    {
        _logger.LogInformation("🔄 Testing migration management...");

        var migrationService = _serviceProvider.GetRequiredService<IEnhancedServicesMigrationService>();

        try
        {
            // Test initial state
            var initialReport = migrationService.GetMigrationReport();
            _logger.LogInformation("📋 Initial migration status: {Status}", initialReport.OverallStatus);

            // Test health check
            var healthResult = await migrationService.PerformHealthCheckAsync();
            _logger.LogInformation("🏥 Health check - Overall: {Health}, Services: {Count}",
                healthResult.OverallHealth, healthResult.ServiceChecks.Count);

            healthResult.OverallHealth.Should().NotBe(HealthStatus.Unhealthy, "System should not be unhealthy");

            // Test feature flag management
            var useEnhancedData = migrationService.ShouldUseEnhancedDataRetrieval();
            var useAdaptiveRate = migrationService.ShouldUseAdaptiveRateLimit();
            var useAdaptiveSignal = migrationService.ShouldUseAdaptiveSignalGeneration();

            _logger.LogInformation("🎛️ Feature flags - Data: {Data}, Rate: {Rate}, Signal: {Signal}",
                useEnhancedData, useAdaptiveRate, useAdaptiveSignal);

            // Test service disable/enable
            if (useEnhancedData)
            {
                await migrationService.DisableServiceAsync("EnhancedDataRetrieval", "Integration test");
                migrationService.ShouldUseEnhancedDataRetrieval().Should().BeFalse();

                await migrationService.EnableServiceAsync("EnhancedDataRetrieval", "Test completed");
                migrationService.ShouldUseEnhancedDataRetrieval().Should().BeTrue();
            }

            _logger.LogInformation("✅ Migration management test passed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Migration management test failed");
            throw;
        }
    }

    [Fact]
    public async Task FullSystem_PerformanceUnderLoad_MeetsExpectations()
    {
        _logger.LogInformation("⚡ Testing system performance under load...");

        var signalGenerator = _serviceProvider.GetRequiredService<ISignalGenerator>();
        var enhancedDataService = _serviceProvider.GetService<IEnhancedDataRetrievalService>();

        try
        {
            var stopwatch = Stopwatch.StartNew();

            // Test concurrent signal generation
            var signalTasks = Enumerable.Range(0, 3).Select(async i =>
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(1));
                return await signalGenerator.RunAsync(2, cts.Token);
            });

            var signalResults = await Task.WhenAll(signalTasks);
            var totalSignals = signalResults.Sum(r => r.Count());

            stopwatch.Stop();

            _logger.LogInformation("🏃 Performance test - Generated {Signals} signals in {Duration}ms across {Tasks} concurrent tasks",
                totalSignals, stopwatch.ElapsedMilliseconds, signalTasks.Count());

            // Performance expectations
            stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromMinutes(3), "Signal generation should complete within reasonable time");
            totalSignals.Should().BeGreaterThan(0, "Should generate at least some signals");

            _logger.LogInformation("✅ Performance test passed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Performance test failed");
            throw;
        }
    }

    private static IConfiguration CreateTestConfiguration()
    {
        return new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                // Enhanced Services
                ["EnhancedServices:EnableEnhancedDataRetrieval"] = "true",
                ["EnhancedServices:EnableAdaptiveRateLimit"] = "true",
                ["EnhancedServices:EnableAdaptiveSignalGeneration"] = "true",
                ["EnhancedServices:EnableSyntheticData"] = "false",
                
                // Basic configuration to prevent errors
                ["Redis:ConnectionString"] = "localhost:6379",
                ["Universe:UsePolygon"] = "false",
                ["Universe:MaxSymbols"] = "10",
                
                // Enhanced configurations
                ["EnhancedDataRetrieval:MaxConcurrentRequests"] = "5",
                ["EnhancedDataRetrieval:PrimaryApiTimeout"] = "00:00:30",
                ["AdaptiveRateLimit:AdjustmentInterval"] = "00:01:00",
                ["AdaptiveSignal:MaxSymbolsToProcess"] = "20",
                ["RobustSignal:MinimumAcceptableSignals"] = "1"
            })
            .Build();
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}

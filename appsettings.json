{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Redis": {"ConnectionString": "localhost:6379", "UniverseCacheConnection": "localhost:6379"}, "AlpacaNews": {"Endpoint": "wss://stream.data.alpaca.markets/v1beta1/news", "KeyIdEnv": "APCA_API_KEY_ID", "SecretEnv": "APCA_API_SECRET"}, "Gemini": {"Endpoint": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent", "ApiKeyEnv": "GEMINI_API_KEY", "TimeoutSec": 45}, "QuoteVolatility": {"WindowSeconds": 120, "StdDevSigma": 2.0, "HaltDurationSeconds": 120}, "OptionsWheel": {"StrikeBandPercent": 15}, "Slippage": {"ModelPath": "Model/slippage_model.zip", "DefaultCents": 0.3}, "AllowedHosts": "*"}
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FluentAssertions;
using Moq;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using SmaTrendFollower.Configuration;
using System.Diagnostics;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Tests.Performance;

/// <summary>
/// Performance and stress tests for enhanced data retrieval system
/// Validates system behavior under load and failure conditions
/// </summary>
public sealed class DataRetrievalPerformanceTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly Mock<IMarketDataService> _mockPrimaryService;
    private readonly Mock<IStockBarCacheService> _mockCacheService;
    private readonly IEnhancedDataRetrievalService _enhancedService;

    public DataRetrievalPerformanceTests()
    {
        var services = new ServiceCollection();
        
        _mockPrimaryService = new Mock<IMarketDataService>();
        _mockCacheService = new Mock<IStockBarCacheService>();

        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));
        services.AddSingleton(_mockPrimaryService.Object);
        services.AddSingleton(_mockCacheService.Object);
        services.AddSingleton<ISyntheticDataGenerator>(new Mock<ISyntheticDataGenerator>().Object);
        
        services.AddSingleton<IDataStalenessValidationService>(provider =>
            new DataStalenessValidationService(
                new DataStalenessConfiguration { EnableStrictStalenessChecks = false },
                provider.GetRequiredService<ILogger<DataStalenessValidationService>>()));

        services.AddSingleton<IEnhancedDataRetrievalService, EnhancedDataRetrievalService>();
        services.AddSingleton(new DataRetrievalConfiguration
        {
            MaxConcurrentRequests = 50,
            PrimaryApiTimeout = TimeSpan.FromSeconds(10),
            BatchTimeout = TimeSpan.FromMinutes(5)
        });

        _serviceProvider = services.BuildServiceProvider();
        _enhancedService = _serviceProvider.GetRequiredService<IEnhancedDataRetrievalService>();
    }

    [Fact]
    public async Task BatchRetrieval_HighConcurrency_CompletesWithinTimeout()
    {
        // Arrange
        var symbols = GenerateSymbols(100);
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow.AddDays(-1);
        var responseDelay = TimeSpan.FromMilliseconds(100);

        SetupMockResponses(symbols, responseDelay);

        // Act
        var stopwatch = Stopwatch.StartNew();
        var results = await _enhancedService.GetStockBarsBatchAsync(symbols, startDate, endDate);
        stopwatch.Stop();

        // Assert
        results.Should().HaveCount(100);
        stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromMinutes(2)); // Should complete well within timeout
        
        var successCount = results.Values.Count(r => r.IsSuccess);
        successCount.Should().BeGreaterThan(90); // At least 90% success rate
        
        // Verify concurrent execution (should be much faster than sequential)
        var sequentialTime = responseDelay.TotalMilliseconds * symbols.Count;
        stopwatch.Elapsed.TotalMilliseconds.Should().BeLessThan(sequentialTime * 0.3); // At least 70% faster
    }

    [Fact]
    public async Task BatchRetrieval_MixedFailureScenarios_HandlesGracefully()
    {
        // Arrange
        var symbols = GenerateSymbols(50);
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow.AddDays(-1);

        SetupMixedFailureScenarios(symbols);

        // Act
        var stopwatch = Stopwatch.StartNew();
        var results = await _enhancedService.GetStockBarsBatchAsync(symbols, startDate, endDate);
        stopwatch.Stop();

        // Assert
        results.Should().HaveCount(50);
        
        var successCount = results.Values.Count(r => r.IsSuccess);
        var primaryApiCount = results.Values.Count(r => r.IsSuccess && r.FinalDataSource == DataSource.PrimaryApi);
        var cacheCount = results.Values.Count(r => r.IsSuccess && r.FinalDataSource == DataSource.CacheRelaxed);
        
        successCount.Should().BeGreaterThan(30); // At least 60% success rate with fallbacks
        primaryApiCount.Should().BeGreaterThan(0);
        cacheCount.Should().BeGreaterThan(0);
        
        // Should complete within reasonable time even with failures
        stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromMinutes(3));
    }

    [Fact]
    public async Task SingleRetrieval_UnderLoad_MaintainsPerformance()
    {
        // Arrange
        var symbol = "AAPL";
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow.AddDays(-1);
        var concurrentRequests = 20;

        _mockPrimaryService.Setup(x => x.GetStockBarsAsync(symbol, startDate, endDate))
            .Returns(async () =>
            {
                await Task.Delay(50); // Simulate API latency
                return CreateMockBarPage(symbol, 20);
            });

        // Act
        var tasks = Enumerable.Range(0, concurrentRequests)
            .Select(_ => _enhancedService.GetStockBarsAsync(symbol, startDate, endDate))
            .ToArray();

        var stopwatch = Stopwatch.StartNew();
        var results = await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        results.Should().HaveLength(concurrentRequests);
        results.Should().OnlyContain(r => r.IsSuccess);
        
        // Should handle concurrent requests efficiently
        stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromSeconds(5));
        
        // Verify all requests completed successfully
        results.Should().OnlyContain(r => r.Data != null && r.Data.Items.Any());
    }

    [Fact]
    public async Task BatchRetrieval_MemoryUsage_StaysWithinBounds()
    {
        // Arrange
        var symbols = GenerateSymbols(200); // Large batch
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow.AddDays(-1);

        SetupMockResponses(symbols, TimeSpan.FromMilliseconds(10));

        var initialMemory = GC.GetTotalMemory(true);

        // Act
        var results = await _enhancedService.GetStockBarsBatchAsync(symbols, startDate, endDate);

        var finalMemory = GC.GetTotalMemory(false);
        var memoryIncrease = finalMemory - initialMemory;

        // Assert
        results.Should().HaveCount(200);
        
        // Memory increase should be reasonable (less than 100MB for this test)
        memoryIncrease.Should().BeLessThan(100 * 1024 * 1024);
        
        // Force garbage collection and verify memory is released
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        
        var afterGcMemory = GC.GetTotalMemory(true);
        var memoryRetained = afterGcMemory - initialMemory;
        
        // Most memory should be released after GC
        memoryRetained.Should().BeLessThan(memoryIncrease * 0.5);
    }

    [Fact]
    public async Task BatchRetrieval_ErrorRecovery_HandlesSystemicFailures()
    {
        // Arrange
        var symbols = GenerateSymbols(30);
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow.AddDays(-1);

        // Simulate systemic failure - all primary API calls fail
        _mockPrimaryService.Setup(x => x.GetStockBarsAsync(It.IsAny<string>(), startDate, endDate))
            .ThrowsAsync(new HttpRequestException("Service unavailable"));

        // Setup cache fallback for all symbols
        foreach (var symbol in symbols)
        {
            _mockCacheService.Setup(x => x.HasBarsAsync(symbol, startDate, endDate))
                .ReturnsAsync(true);
            
            _mockCacheService.Setup(x => x.GetBarsAsync(symbol, startDate, endDate))
                .ReturnsAsync(CreateMockBars(symbol, 15));
        }

        // Act
        var stopwatch = Stopwatch.StartNew();
        var results = await _enhancedService.GetStockBarsBatchAsync(symbols, startDate, endDate);
        stopwatch.Stop();

        // Assert
        results.Should().HaveCount(30);
        
        // All should succeed via cache fallback
        var successCount = results.Values.Count(r => r.IsSuccess);
        successCount.Should().Be(30);
        
        // All should use cache as data source
        results.Values.Should().OnlyContain(r => r.FinalDataSource == DataSource.CacheRelaxed);
        
        // Should complete reasonably quickly even with all failures
        stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromMinutes(2));
    }

    [Fact]
    public async Task BatchRetrieval_CancellationHandling_RespondsPromptly()
    {
        // Arrange
        var symbols = GenerateSymbols(100);
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow.AddDays(-1);

        // Setup slow responses
        _mockPrimaryService.Setup(x => x.GetStockBarsAsync(It.IsAny<string>(), startDate, endDate))
            .Returns(async () =>
            {
                await Task.Delay(TimeSpan.FromSeconds(30)); // Very slow
                return CreateMockBarPage("TEST", 20);
            });

        using var cts = new CancellationTokenSource();

        // Act
        var task = _enhancedService.GetStockBarsBatchAsync(symbols, startDate, endDate, cts.Token);
        
        // Cancel after 1 second
        await Task.Delay(1000);
        cts.Cancel();

        var stopwatch = Stopwatch.StartNew();
        try
        {
            await task;
        }
        catch (OperationCanceledException)
        {
            // Expected
        }
        stopwatch.Stop();

        // Assert
        // Should respond to cancellation quickly (within 5 seconds)
        stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromSeconds(5));
    }

    private static List<string> GenerateSymbols(int count)
    {
        var symbols = new List<string>();
        for (int i = 0; i < count; i++)
        {
            symbols.Add($"TEST{i:D3}");
        }
        return symbols;
    }

    private void SetupMockResponses(List<string> symbols, TimeSpan delay)
    {
        foreach (var symbol in symbols)
        {
            _mockPrimaryService.Setup(x => x.GetStockBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .Returns(async () =>
                {
                    await Task.Delay(delay);
                    return CreateMockBarPage(symbol, 20);
                });
        }
    }

    private void SetupMixedFailureScenarios(List<string> symbols)
    {
        for (int i = 0; i < symbols.Count; i++)
        {
            var symbol = symbols[i];
            
            if (i % 3 == 0)
            {
                // Success case
                _mockPrimaryService.Setup(x => x.GetStockBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                    .ReturnsAsync(CreateMockBarPage(symbol, 20));
            }
            else if (i % 3 == 1)
            {
                // Primary fails, cache succeeds
                _mockPrimaryService.Setup(x => x.GetStockBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                    .ThrowsAsync(new HttpRequestException("Rate limited"));
                
                _mockCacheService.Setup(x => x.HasBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                    .ReturnsAsync(true);
                
                _mockCacheService.Setup(x => x.GetBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                    .ReturnsAsync(CreateMockBars(symbol, 15));
            }
            else
            {
                // Both fail
                _mockPrimaryService.Setup(x => x.GetStockBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                    .ThrowsAsync(new HttpRequestException("Service unavailable"));
                
                _mockCacheService.Setup(x => x.HasBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                    .ReturnsAsync(false);
            }
        }
    }

    private static MockBarPage CreateMockBarPage(string symbol, int count)
    {
        return new MockBarPage(CreateMockBars(symbol, count));
    }

    private static List<MockBar> CreateMockBars(string symbol, int count)
    {
        var bars = new List<MockBar>();
        var baseTime = DateTime.UtcNow.AddDays(-count);
        
        for (int i = 0; i < count; i++)
        {
            bars.Add(new MockBar
            {
                Symbol = symbol,
                TimeUtc = baseTime.AddDays(i),
                Open = 100 + i,
                High = 105 + i,
                Low = 95 + i,
                Close = 102 + i,
                Volume = 1000000,
                Vwap = 101 + i,
                TradeCount = 5000
            });
        }
        
        return bars;
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}

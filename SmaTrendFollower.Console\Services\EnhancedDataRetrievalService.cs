using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Monitoring;
using System.Collections.Concurrent;
using System.Diagnostics;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced data retrieval service with multi-tier fallback strategies
/// Provides bulletproof data access with graceful degradation
/// </summary>
public sealed class EnhancedDataRetrievalService : IEnhancedDataRetrievalService
{
    private readonly IMarketDataService _primaryDataService;
    private readonly IStockBarCacheService _cacheService;
    private readonly IDataStalenessValidationService _stalenessValidator;
    private readonly ISyntheticDataGenerator _syntheticDataGenerator;
    private readonly ILogger<EnhancedDataRetrievalService> _logger;
    private readonly DataRetrievalConfiguration _config;
    private readonly SemaphoreSlim _emergencyModeSemaphore = new(1, 1);
    private volatile bool _emergencyModeActive = false;
    private DateTime _lastEmergencyModeCheck = DateTime.MinValue;

    public EnhancedDataRetrievalService(
        IMarketDataService primaryDataService,
        IStockBarCacheService cacheService,
        IDataStalenessValidationService stalenessValidator,
        ISyntheticDataGenerator syntheticDataGenerator,
        ILogger<EnhancedDataRetrievalService> logger,
        DataRetrievalConfiguration config)
    {
        _primaryDataService = primaryDataService;
        _cacheService = cacheService;
        _stalenessValidator = stalenessValidator;
        _syntheticDataGenerator = syntheticDataGenerator;
        _logger = logger;
        _config = config;
    }

    /// <summary>
    /// Retrieves stock bars with comprehensive fallback strategy
    /// </summary>
    public async Task<DataRetrievalResult<IPage<IBar>>> GetStockBarsAsync(
        string symbol, 
        DateTime startDate, 
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var attempts = new List<DataRetrievalAttempt>();

        try
        {
            // Tier 1: Primary API with fresh data
            var primaryResult = await TryPrimaryApiAsync(symbol, startDate, endDate, attempts, cancellationToken);
            if (primaryResult.IsSuccess)
            {
                return CreateSuccessResult(primaryResult.Data!, attempts, stopwatch.Elapsed, DataSource.PrimaryApi);
            }

            // Tier 2: Cache with relaxed staleness (if emergency mode or configured)
            var cacheResult = await TryCacheWithRelaxedStalenessAsync(symbol, startDate, endDate, attempts, cancellationToken);
            if (cacheResult.IsSuccess)
            {
                return CreateSuccessResult(cacheResult.Data!, attempts, stopwatch.Elapsed, DataSource.CacheRelaxed);
            }

            // Tier 3: Synthetic data generation
            var syntheticResult = await TrySyntheticDataAsync(symbol, startDate, endDate, attempts, cancellationToken);
            if (syntheticResult.IsSuccess)
            {
                return CreateSuccessResult(syntheticResult.Data!, attempts, stopwatch.Elapsed, DataSource.Synthetic);
            }

            // Tier 4: Emergency cache (any available data)
            var emergencyResult = await TryEmergencyCacheAsync(symbol, startDate, endDate, attempts, cancellationToken);
            if (emergencyResult.IsSuccess)
            {
                return CreateSuccessResult(emergencyResult.Data!, attempts, stopwatch.Elapsed, DataSource.EmergencyCache);
            }

            // All tiers failed
            return CreateFailureResult<IPage<IBar>>(attempts, stopwatch.Elapsed, "All data retrieval tiers failed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Critical error in enhanced data retrieval for {Symbol}", symbol);
            return CreateFailureResult<IPage<IBar>>(attempts, stopwatch.Elapsed, $"Critical error: {ex.Message}");
        }
    }

    /// <summary>
    /// Batch retrieval with partial success handling
    /// </summary>
    public async Task<IDictionary<string, DataRetrievalResult<IPage<IBar>>>> GetStockBarsBatchAsync(
        IEnumerable<string> symbols,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default)
    {
        var symbolList = symbols.ToList();
        var results = new ConcurrentDictionary<string, DataRetrievalResult<IPage<IBar>>>();
        var semaphore = new SemaphoreSlim(_config.MaxConcurrentRequests, _config.MaxConcurrentRequests);

        _logger.LogInformation("Starting batch retrieval for {Count} symbols", symbolList.Count);

        var tasks = symbolList.Select(async symbol =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                var result = await GetStockBarsAsync(symbol, startDate, endDate, cancellationToken);
                results[symbol] = result;
            }
            finally
            {
                semaphore.Release();
            }
        });

        // Use timeout for batch operation
        using var timeoutCts = new CancellationTokenSource(_config.BatchTimeout);
        using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

        try
        {
            await Task.WhenAll(tasks).WaitAsync(combinedCts.Token);
        }
        catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested)
        {
            _logger.LogWarning("Batch retrieval timed out after {Timeout}. Returning partial results.", _config.BatchTimeout);
        }

        var successCount = results.Values.Count(r => r.IsSuccess);
        var failureCount = results.Values.Count(r => !r.IsSuccess);

        _logger.LogInformation("Batch retrieval completed: {Success} successful, {Failed} failed out of {Total}",
            successCount, failureCount, symbolList.Count);

        MetricsRegistry.BatchRetrievalResults.WithLabels("success").Set(successCount);
        MetricsRegistry.BatchRetrievalResults.WithLabels("failure").Set(failureCount);

        return results;
    }

    private async Task<AttemptResult<IPage<IBar>>> TryPrimaryApiAsync(
        string symbol, 
        DateTime startDate, 
        DateTime endDate, 
        List<DataRetrievalAttempt> attempts,
        CancellationToken cancellationToken)
    {
        var attempt = new DataRetrievalAttempt { Source = DataSource.PrimaryApi, StartTime = DateTime.UtcNow };
        attempts.Add(attempt);

        try
        {
            using var timeoutCts = new CancellationTokenSource(_config.PrimaryApiTimeout);
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            var data = await _primaryDataService.GetStockBarsAsync(symbol, startDate, endDate);
            
            // Validate data freshness
            if (data.Items.Any())
            {
                var latestBar = data.Items.OrderByDescending(b => b.TimeUtc).First();
                var validationResult = await _stalenessValidator.ValidateDataFreshnessAsync(
                    latestBar.TimeUtc, DataType.HistoricalBars, "PrimaryApi");

                if (validationResult.IsStale && !_emergencyModeActive)
                {
                    attempt.ErrorMessage = $"Data too stale: {validationResult.DataAge}";
                    attempt.Success = false;
                    return new AttemptResult<IPage<IBar>> { IsSuccess = false };
                }
            }

            attempt.Success = true;
            attempt.DataQuality = DataQuality.High;
            return new AttemptResult<IPage<IBar>> { IsSuccess = true, Data = data };
        }
        catch (Exception ex)
        {
            attempt.ErrorMessage = ex.Message;
            attempt.Success = false;
            _logger.LogWarning(ex, "Primary API failed for {Symbol}", symbol);

            // Check if we should activate emergency mode
            await CheckAndActivateEmergencyModeAsync(ex);

            return new AttemptResult<IPage<IBar>> { IsSuccess = false };
        }
        finally
        {
            attempt.EndTime = DateTime.UtcNow;
        }
    }

    private async Task<AttemptResult<IPage<IBar>>> TryCacheWithRelaxedStalenessAsync(
        string symbol,
        DateTime startDate,
        DateTime endDate,
        List<DataRetrievalAttempt> attempts,
        CancellationToken cancellationToken)
    {
        var attempt = new DataRetrievalAttempt { Source = DataSource.CacheRelaxed, StartTime = DateTime.UtcNow };
        attempts.Add(attempt);

        try
        {
            // Check if we have cached data by trying to get it
            var cachedData = await _cacheService.GetCachedBarsAsync(symbol, "Day", startDate, endDate);
            if (!cachedData.Any())
            {
                attempt.ErrorMessage = "No cached data available";
                attempt.Success = false;
                return new AttemptResult<IPage<IBar>> { IsSuccess = false };
            }
            
            // Use relaxed staleness validation in emergency mode
            var relaxedThreshold = _emergencyModeActive 
                ? _config.EmergencyModeMaxStaleness 
                : _config.RelaxedStalenessThreshold;

            if (cachedData.Any())
            {
                var latestBar = cachedData.OrderByDescending(b => b.TimeUtc).First();
                var dataAge = DateTime.UtcNow - latestBar.TimeUtc;

                if (dataAge > relaxedThreshold)
                {
                    attempt.ErrorMessage = $"Cached data too stale: {dataAge} > {relaxedThreshold}";
                    attempt.Success = false;
                    return new AttemptResult<IPage<IBar>> { IsSuccess = false };
                }
            }

            attempt.Success = true;
            attempt.DataQuality = _emergencyModeActive ? DataQuality.Low : DataQuality.Medium;
            
            // Convert to IPage format
            var page = new CachedBarPage(cachedData, symbol);
            return new AttemptResult<IPage<IBar>> { IsSuccess = true, Data = page };
        }
        catch (Exception ex)
        {
            attempt.ErrorMessage = ex.Message;
            attempt.Success = false;
            _logger.LogWarning(ex, "Cache with relaxed staleness failed for {Symbol}", symbol);
            return new AttemptResult<IPage<IBar>> { IsSuccess = false };
        }
        finally
        {
            attempt.EndTime = DateTime.UtcNow;
        }
    }

    private async Task<AttemptResult<IPage<IBar>>> TrySyntheticDataAsync(
        string symbol,
        DateTime startDate,
        DateTime endDate,
        List<DataRetrievalAttempt> attempts,
        CancellationToken cancellationToken)
    {
        var attempt = new DataRetrievalAttempt { Source = DataSource.Synthetic, StartTime = DateTime.UtcNow };
        attempts.Add(attempt);

        try
        {
            if (!_config.EnableSyntheticData)
            {
                attempt.ErrorMessage = "Synthetic data generation disabled";
                attempt.Success = false;
                return new AttemptResult<IPage<IBar>> { IsSuccess = false };
            }

            var syntheticData = await _syntheticDataGenerator.GenerateBarsAsync(symbol, startDate, endDate, cancellationToken);
            
            attempt.Success = true;
            attempt.DataQuality = DataQuality.Synthetic;
            return new AttemptResult<IPage<IBar>> { IsSuccess = true, Data = syntheticData };
        }
        catch (Exception ex)
        {
            attempt.ErrorMessage = ex.Message;
            attempt.Success = false;
            _logger.LogWarning(ex, "Synthetic data generation failed for {Symbol}", symbol);
            return new AttemptResult<IPage<IBar>> { IsSuccess = false };
        }
        finally
        {
            attempt.EndTime = DateTime.UtcNow;
        }
    }

    private async Task<AttemptResult<IPage<IBar>>> TryEmergencyCacheAsync(
        string symbol,
        DateTime startDate,
        DateTime endDate,
        List<DataRetrievalAttempt> attempts,
        CancellationToken cancellationToken)
    {
        var attempt = new DataRetrievalAttempt { Source = DataSource.EmergencyCache, StartTime = DateTime.UtcNow };
        attempts.Add(attempt);

        try
        {
            // Get any available cached data, regardless of staleness
            var cachedData = await _cacheService.GetCachedBarsAsync(symbol, "Day", startDate, endDate);
            
            if (!cachedData.Any())
            {
                attempt.ErrorMessage = "No emergency cache data available";
                attempt.Success = false;
                return new AttemptResult<IPage<IBar>> { IsSuccess = false };
            }

            _logger.LogWarning("Using emergency cache data for {Symbol} - data may be very stale", symbol);
            
            attempt.Success = true;
            attempt.DataQuality = DataQuality.Emergency;
            
            var page = new CachedBarPage(cachedData, symbol);
            return new AttemptResult<IPage<IBar>> { IsSuccess = true, Data = page };
        }
        catch (Exception ex)
        {
            attempt.ErrorMessage = ex.Message;
            attempt.Success = false;
            _logger.LogError(ex, "Emergency cache failed for {Symbol}", symbol);
            return new AttemptResult<IPage<IBar>> { IsSuccess = false };
        }
        finally
        {
            attempt.EndTime = DateTime.UtcNow;
        }
    }

    private async Task CheckAndActivateEmergencyModeAsync(Exception ex)
    {
        // Avoid checking too frequently
        if (DateTime.UtcNow - _lastEmergencyModeCheck < TimeSpan.FromMinutes(1))
            return;

        await _emergencyModeSemaphore.WaitAsync();
        try
        {
            _lastEmergencyModeCheck = DateTime.UtcNow;

            // Check if this is a systemic failure
            var isSystemicFailure = IsSystemicFailure(ex);
            
            if (isSystemicFailure && !_emergencyModeActive)
            {
                _emergencyModeActive = true;
                _logger.LogWarning("Emergency mode activated due to systemic failure: {Error}", ex.Message);
                MetricsRegistry.EmergencyModeActivations.Inc();
                
                // Schedule deactivation check
                _ = Task.Run(async () =>
                {
                    await Task.Delay(_config.EmergencyModeTimeout);
                    await DeactivateEmergencyModeAsync();
                });
            }
        }
        finally
        {
            _emergencyModeSemaphore.Release();
        }
    }

    private async Task DeactivateEmergencyModeAsync()
    {
        await _emergencyModeSemaphore.WaitAsync();
        try
        {
            if (_emergencyModeActive)
            {
                _emergencyModeActive = false;
                _logger.LogInformation("Emergency mode deactivated");
                MetricsRegistry.EmergencyModeDeactivations.Inc();
            }
        }
        finally
        {
            _emergencyModeSemaphore.Release();
        }
    }

    private static bool IsSystemicFailure(Exception ex)
    {
        return ex.Message.Contains("TooManyRequests") ||
               ex.Message.Contains("429") ||
               ex is HttpRequestException ||
               ex is TaskCanceledException ||
               ex.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase);
    }

    private static DataRetrievalResult<T> CreateSuccessResult<T>(
        T data, 
        List<DataRetrievalAttempt> attempts, 
        TimeSpan totalDuration,
        DataSource finalSource)
    {
        return new DataRetrievalResult<T>
        {
            IsSuccess = true,
            Data = data,
            Attempts = attempts,
            TotalDuration = totalDuration,
            FinalDataSource = finalSource,
            DataQuality = attempts.LastOrDefault()?.DataQuality ?? DataQuality.Unknown
        };
    }

    private static DataRetrievalResult<T> CreateFailureResult<T>(
        List<DataRetrievalAttempt> attempts, 
        TimeSpan totalDuration,
        string errorMessage)
    {
        return new DataRetrievalResult<T>
        {
            IsSuccess = false,
            Attempts = attempts,
            TotalDuration = totalDuration,
            ErrorMessage = errorMessage
        };
    }
}

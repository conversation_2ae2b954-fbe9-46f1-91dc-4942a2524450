using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Alpaca.Markets;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Tests.TestHelpers;

/// <summary>
/// Mock implementation of IMarketDataService for testing market session validation
/// Generates realistic historical data patterns including proper Monday trading days
/// </summary>
public class MockMarketDataService : IMarketDataService
{
    private readonly Random _random = new();

    public async Task<IPage<IBar>> GetStockBarsAsync(string symbol, DateTime from, DateTime to, CancellationToken cancellationToken = default)
    {
        await Task.Delay(10, cancellationToken); // Simulate API delay
        
        var bars = GenerateHistoricalBars(symbol, from, to);
        return new MockPage<IBar>(bars);
    }

    public async Task<IPage<IBar>> GetStockMinuteBarsAsync(string symbol, DateTime from, DateTime to, CancellationToken cancellationToken = default)
    {
        await Task.Delay(10, cancellationToken);
        
        var bars = GenerateMinuteBars(symbol, from, to);
        return new MockPage<IBar>(bars);
    }

    /// <summary>
    /// Generates realistic historical bars that exclude weekends and known holidays
    /// This simulates actual market data patterns for validation testing
    /// </summary>
    private List<IBar> GenerateHistoricalBars(string symbol, DateTime from, DateTime to)
    {
        var bars = new List<IBar>();
        var currentDate = from.Date;
        var basePrice = symbol == "SPY" ? 450m : 150m;
        var currentPrice = basePrice;

        while (currentDate <= to.Date)
        {
            // Only generate bars for trading days (exclude weekends and holidays)
            if (IsTradingDay(currentDate))
            {
                // Generate realistic price movement
                var priceChange = (decimal)(_random.NextDouble() * 4 - 2); // -2 to +2
                currentPrice = Math.Max(currentPrice + priceChange, basePrice * 0.8m);
                
                var open = currentPrice;
                var high = open + (decimal)(_random.NextDouble() * 2);
                var low = open - (decimal)(_random.NextDouble() * 2);
                var close = low + (decimal)(_random.NextDouble() * (high - low));
                var volume = (ulong)(_random.Next(50000000, 200000000));

                bars.Add(new MockBar
                {
                    Symbol = symbol,
                    TimeUtc = currentDate.AddHours(20), // 4:00 PM ET close
                    Open = open,
                    High = high,
                    Low = low,
                    Close = close,
                    Volume = volume
                });

                currentPrice = close;
            }

            currentDate = currentDate.AddDays(1);
        }

        return bars;
    }

    /// <summary>
    /// Generates minute-level bars for intraday testing
    /// </summary>
    private List<IBar> GenerateMinuteBars(string symbol, DateTime from, DateTime to)
    {
        var bars = new List<IBar>();
        var current = from;
        var basePrice = symbol == "SPY" ? 450m : 150m;

        while (current <= to)
        {
            if (IsTradingDay(current.Date) && IsMarketHours(current))
            {
                var price = basePrice + (decimal)(_random.NextDouble() * 10 - 5);
                bars.Add(new MockBar
                {
                    Symbol = symbol,
                    TimeUtc = current,
                    Open = price,
                    High = price + 0.5m,
                    Low = price - 0.5m,
                    Close = price + (decimal)(_random.NextDouble() * 1 - 0.5),
                    Volume = (ulong)_random.Next(1000, 10000)
                });
            }
            current = current.AddMinutes(1);
        }

        return bars;
    }

    /// <summary>
    /// Determines if a date is a trading day (excludes weekends and major holidays)
    /// This matches the logic that should be tested in market session validation
    /// </summary>
    private bool IsTradingDay(DateTime date)
    {
        // Exclude weekends
        if (date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday)
            return false;

        // Exclude major holidays (simplified list for testing)
        var holidays = GetKnownHolidays(date.Year);
        return !holidays.Contains(date.Date);
    }

    /// <summary>
    /// Gets known market holidays for a given year
    /// </summary>
    private HashSet<DateTime> GetKnownHolidays(int year)
    {
        var holidays = new HashSet<DateTime>();

        // Fixed holidays
        holidays.Add(new DateTime(year, 1, 1));   // New Year's Day
        holidays.Add(new DateTime(year, 7, 4));   // Independence Day
        holidays.Add(new DateTime(year, 12, 25)); // Christmas

        // Variable holidays (simplified calculation)
        // MLK Day - 3rd Monday in January
        var firstMondayJan = GetFirstMondayOfMonth(year, 1);
        holidays.Add(firstMondayJan.AddDays(14));

        // Presidents Day - 3rd Monday in February
        var firstMondayFeb = GetFirstMondayOfMonth(year, 2);
        holidays.Add(firstMondayFeb.AddDays(14));

        // Memorial Day - Last Monday in May
        var lastMondayMay = GetLastMondayOfMonth(year, 5);
        holidays.Add(lastMondayMay);

        // Labor Day - 1st Monday in September
        var firstMondaySep = GetFirstMondayOfMonth(year, 9);
        holidays.Add(firstMondaySep);

        return holidays;
    }

    private DateTime GetFirstMondayOfMonth(int year, int month)
    {
        var firstDay = new DateTime(year, month, 1);
        var daysUntilMonday = ((int)DayOfWeek.Monday - (int)firstDay.DayOfWeek + 7) % 7;
        return firstDay.AddDays(daysUntilMonday);
    }

    private DateTime GetLastMondayOfMonth(int year, int month)
    {
        var lastDay = new DateTime(year, month, DateTime.DaysInMonth(year, month));
        var daysBackToMonday = ((int)lastDay.DayOfWeek - (int)DayOfWeek.Monday + 7) % 7;
        return lastDay.AddDays(-daysBackToMonday);
    }

    /// <summary>
    /// Checks if a time is during market hours (9:30 AM - 4:00 PM ET)
    /// </summary>
    private bool IsMarketHours(DateTime utcTime)
    {
        var easternTime = TimeZoneInfo.ConvertTimeFromUtc(utcTime,
            TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time"));

        var marketOpen = new TimeSpan(9, 30, 0);
        var marketClose = new TimeSpan(16, 0, 0);

        return easternTime.TimeOfDay >= marketOpen && easternTime.TimeOfDay <= marketClose;
    }

    // Implement other required interface methods with minimal functionality
    public Task<IPage<IBar>> GetIndexBarsAsync(string symbol, DateTime from, DateTime to, CancellationToken cancellationToken = default)
        => Task.FromResult<IPage<IBar>>(new MockPage<IBar>(new List<IBar>()));

    public Task<decimal?> GetLatestPriceAsync(string symbol, CancellationToken cancellationToken = default)
        => Task.FromResult<decimal?>(150m);

    public Task<IReadOnlyList<IQuote>> GetLatestQuotesAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
        => Task.FromResult<IReadOnlyList<IQuote>>(new List<IQuote>());

    public Task<MarketDataValidationResult> ValidateDataFreshnessAsync(string symbol, DateTime requestTime, CancellationToken cancellationToken = default)
        => Task.FromResult(new MarketDataValidationResult { IsValid = true, DataAge = TimeSpan.Zero });

    public Task<bool> IsDataStaleAsync(string symbol, TimeSpan threshold, CancellationToken cancellationToken = default)
        => Task.FromResult(false);
}

/// <summary>
/// Mock implementation of IPage for testing
/// </summary>
public class MockPage<T> : IPage<T>
{
    public MockPage(IEnumerable<T> items)
    {
        Items = items;
    }

    public IEnumerable<T> Items { get; }
    public string? NextPageToken => null;
}

/// <summary>
/// Mock implementation of IBar for testing
/// </summary>
public class MockBar : IBar
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime TimeUtc { get; set; }
    public decimal Open { get; set; }
    public decimal High { get; set; }
    public decimal Low { get; set; }
    public decimal Close { get; set; }
    public ulong Volume { get; set; }
    public decimal Vwap { get; set; }
    public ulong TradeCount { get; set; }
}

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FluentAssertions;
using Moq;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using SmaTrendFollower.Configuration;
using Alpaca.Markets;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// Integration tests for enhanced data retrieval with fallback strategies
/// Tests all failure scenarios and validates improved reliability
/// </summary>
public sealed class EnhancedDataRetrievalIntegrationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly Mock<IMarketDataService> _mockPrimaryService;
    private readonly Mock<IStockBarCacheService> _mockCacheService;
    private readonly Mock<ISyntheticDataGenerator> _mockSyntheticGenerator;
    private readonly IEnhancedDataRetrievalService _enhancedService;

    public EnhancedDataRetrievalIntegrationTests()
    {
        var services = new ServiceCollection();
        
        // Setup mocks
        _mockPrimaryService = new Mock<IMarketDataService>();
        _mockCacheService = new Mock<IStockBarCacheService>();
        _mockSyntheticGenerator = new Mock<ISyntheticDataGenerator>();

        // Configure services
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        services.AddSingleton(_mockPrimaryService.Object);
        services.AddSingleton(_mockCacheService.Object);
        services.AddSingleton(_mockSyntheticGenerator.Object);
        
        services.AddSingleton<IDataStalenessValidationService>(provider =>
            new DataStalenessValidationService(
                new DataStalenessConfiguration
                {
                    EnableStrictStalenessChecks = true,
                    RejectStaleData = false, // Allow stale data for testing
                    LogStalenessWarnings = true
                },
                provider.GetRequiredService<ILogger<DataStalenessValidationService>>()));

        services.AddSingleton<IEnhancedDataRetrievalService, EnhancedDataRetrievalService>();
        services.AddSingleton(new DataRetrievalConfiguration
        {
            MaxConcurrentRequests = 10,
            PrimaryApiTimeout = TimeSpan.FromSeconds(30),
            BatchTimeout = TimeSpan.FromMinutes(2),
            EnableSyntheticData = true
        });

        _serviceProvider = services.BuildServiceProvider();
        _enhancedService = _serviceProvider.GetRequiredService<IEnhancedDataRetrievalService>();
    }

    [Fact]
    public async Task GetStockBarsAsync_PrimaryApiSuccess_ReturnsDataFromPrimaryApi()
    {
        // Arrange
        var symbol = "AAPL";
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow.AddDays(-1);
        var expectedBars = CreateMockBars(symbol, 20);

        _mockPrimaryService.Setup(x => x.GetStockBarsAsync(symbol, startDate, endDate))
            .ReturnsAsync(new MockBarPage(expectedBars));

        // Act
        var result = await _enhancedService.GetStockBarsAsync(symbol, startDate, endDate);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Items.Should().HaveCount(20);
        result.FinalDataSource.Should().Be(DataSource.PrimaryApi);
        result.DataQuality.Should().Be(DataQuality.High);
        result.Attempts.Should().HaveCount(1);
        result.Attempts[0].Success.Should().BeTrue();
    }

    [Fact]
    public async Task GetStockBarsAsync_PrimaryApiFails_FallsBackToCache()
    {
        // Arrange
        var symbol = "AAPL";
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow.AddDays(-1);
        var cachedBars = CreateMockBars(symbol, 15);

        _mockPrimaryService.Setup(x => x.GetStockBarsAsync(symbol, startDate, endDate))
            .ThrowsAsync(new HttpRequestException("API unavailable"));

        _mockCacheService.Setup(x => x.HasBarsAsync(symbol, startDate, endDate))
            .ReturnsAsync(true);

        _mockCacheService.Setup(x => x.GetBarsAsync(symbol, startDate, endDate))
            .ReturnsAsync(cachedBars);

        // Act
        var result = await _enhancedService.GetStockBarsAsync(symbol, startDate, endDate);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Items.Should().HaveCount(15);
        result.FinalDataSource.Should().Be(DataSource.CacheRelaxed);
        result.DataQuality.Should().Be(DataQuality.Medium);
        result.Attempts.Should().HaveCount(2);
        result.Attempts[0].Success.Should().BeFalse();
        result.Attempts[1].Success.Should().BeTrue();
    }

    [Fact]
    public async Task GetStockBarsAsync_PrimaryAndCacheFail_FallsBackToSynthetic()
    {
        // Arrange
        var symbol = "AAPL";
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow.AddDays(-1);
        var syntheticBars = CreateMockBars(symbol, 25);

        _mockPrimaryService.Setup(x => x.GetStockBarsAsync(symbol, startDate, endDate))
            .ThrowsAsync(new HttpRequestException("API unavailable"));

        _mockCacheService.Setup(x => x.HasBarsAsync(symbol, startDate, endDate))
            .ReturnsAsync(false);

        _mockSyntheticGenerator.Setup(x => x.GenerateBarsAsync(symbol, startDate, endDate, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new MockBarPage(syntheticBars));

        // Act
        var result = await _enhancedService.GetStockBarsAsync(symbol, startDate, endDate);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Items.Should().HaveCount(25);
        result.FinalDataSource.Should().Be(DataSource.Synthetic);
        result.DataQuality.Should().Be(DataQuality.Synthetic);
        result.Attempts.Should().HaveCount(3);
        result.Attempts[2].Success.Should().BeTrue();
    }

    [Fact]
    public async Task GetStockBarsAsync_AllTiersFail_ReturnsFailure()
    {
        // Arrange
        var symbol = "AAPL";
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow.AddDays(-1);

        _mockPrimaryService.Setup(x => x.GetStockBarsAsync(symbol, startDate, endDate))
            .ThrowsAsync(new HttpRequestException("API unavailable"));

        _mockCacheService.Setup(x => x.HasBarsAsync(symbol, startDate, endDate))
            .ReturnsAsync(false);

        _mockSyntheticGenerator.Setup(x => x.GenerateBarsAsync(symbol, startDate, endDate, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Synthetic generation failed"));

        _mockCacheService.Setup(x => x.GetBarsAsync(symbol, startDate, endDate))
            .ReturnsAsync(new List<IBar>());

        // Act
        var result = await _enhancedService.GetStockBarsAsync(symbol, startDate, endDate);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Data.Should().BeNull();
        result.ErrorMessage.Should().NotBeNullOrEmpty();
        result.Attempts.Should().HaveCount(4);
        result.Attempts.Should().OnlyContain(a => !a.Success);
    }

    [Fact]
    public async Task GetStockBarsBatchAsync_PartialSuccess_ReturnsPartialResults()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow.AddDays(-1);

        // AAPL succeeds
        _mockPrimaryService.Setup(x => x.GetStockBarsAsync("AAPL", startDate, endDate))
            .ReturnsAsync(new MockBarPage(CreateMockBars("AAPL", 20)));

        // MSFT fails primary but has cache
        _mockPrimaryService.Setup(x => x.GetStockBarsAsync("MSFT", startDate, endDate))
            .ThrowsAsync(new HttpRequestException("Rate limited"));

        _mockCacheService.Setup(x => x.HasBarsAsync("MSFT", startDate, endDate))
            .ReturnsAsync(true);

        _mockCacheService.Setup(x => x.GetBarsAsync("MSFT", startDate, endDate))
            .ReturnsAsync(CreateMockBars("MSFT", 15));

        // GOOGL fails completely
        _mockPrimaryService.Setup(x => x.GetStockBarsAsync("GOOGL", startDate, endDate))
            .ThrowsAsync(new HttpRequestException("API unavailable"));

        _mockCacheService.Setup(x => x.HasBarsAsync("GOOGL", startDate, endDate))
            .ReturnsAsync(false);

        _mockSyntheticGenerator.Setup(x => x.GenerateBarsAsync("GOOGL", startDate, endDate, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("No reference data"));

        _mockCacheService.Setup(x => x.GetBarsAsync("GOOGL", startDate, endDate))
            .ReturnsAsync(new List<IBar>());

        // Act
        var results = await _enhancedService.GetStockBarsBatchAsync(symbols, startDate, endDate);

        // Assert
        results.Should().HaveCount(3);
        
        results["AAPL"].IsSuccess.Should().BeTrue();
        results["AAPL"].FinalDataSource.Should().Be(DataSource.PrimaryApi);
        
        results["MSFT"].IsSuccess.Should().BeTrue();
        results["MSFT"].FinalDataSource.Should().Be(DataSource.CacheRelaxed);
        
        results["GOOGL"].IsSuccess.Should().BeFalse();
    }

    [Fact]
    public async Task GetStockBarsAsync_RateLimitedWithRetry_EventuallySucceeds()
    {
        // Arrange
        var symbol = "AAPL";
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow.AddDays(-1);
        var expectedBars = CreateMockBars(symbol, 20);

        var callCount = 0;
        _mockPrimaryService.Setup(x => x.GetStockBarsAsync(symbol, startDate, endDate))
            .Returns(() =>
            {
                callCount++;
                if (callCount == 1)
                    throw new HttpRequestException("429 Too Many Requests");
                return Task.FromResult<IPage<IBar>>(new MockBarPage(expectedBars));
            });

        // Setup cache fallback for first attempt
        _mockCacheService.Setup(x => x.HasBarsAsync(symbol, startDate, endDate))
            .ReturnsAsync(false);

        // Act
        var result = await _enhancedService.GetStockBarsAsync(symbol, startDate, endDate);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.FinalDataSource.Should().Be(DataSource.PrimaryApi);
        callCount.Should().Be(1); // Should only call once since we fall back to cache/synthetic
    }

    [Fact]
    public async Task GetStockBarsAsync_StaleDataInEmergencyMode_AcceptsStaleData()
    {
        // Arrange
        var symbol = "AAPL";
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow.AddDays(-1);
        
        // Create very stale bars (2 days old)
        var staleBars = CreateMockBars(symbol, 15, DateTime.UtcNow.AddDays(-2));

        _mockPrimaryService.Setup(x => x.GetStockBarsAsync(symbol, startDate, endDate))
            .ThrowsAsync(new HttpRequestException("Service unavailable"));

        _mockCacheService.Setup(x => x.HasBarsAsync(symbol, startDate, endDate))
            .ReturnsAsync(true);

        _mockCacheService.Setup(x => x.GetBarsAsync(symbol, startDate, endDate))
            .ReturnsAsync(staleBars);

        // Act
        var result = await _enhancedService.GetStockBarsAsync(symbol, startDate, endDate);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.FinalDataSource.Should().Be(DataSource.CacheRelaxed);
        result.DataQuality.Should().Be(DataQuality.Medium);
    }

    private static List<IBar> CreateMockBars(string symbol, int count, DateTime? baseTime = null)
    {
        var bars = new List<IBar>();
        var time = baseTime ?? DateTime.UtcNow.AddDays(-count);
        
        for (int i = 0; i < count; i++)
        {
            bars.Add(new MockBar
            {
                Symbol = symbol,
                TimeUtc = time.AddDays(i),
                Open = 100 + i,
                High = 105 + i,
                Low = 95 + i,
                Close = 102 + i,
                Volume = 1000000,
                Vwap = 101 + i,
                TradeCount = 5000
            });
        }
        
        return bars;
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}

/// <summary>
/// Mock implementation of IBar for testing
/// </summary>
public sealed class MockBar : IBar
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime TimeUtc { get; set; }
    public decimal Open { get; set; }
    public decimal High { get; set; }
    public decimal Low { get; set; }
    public decimal Close { get; set; }
    public ulong Volume { get; set; }
    public decimal Vwap { get; set; }
    public ulong TradeCount { get; set; }
}

/// <summary>
/// Mock implementation of IPage<IBar> for testing
/// </summary>
public sealed class MockBarPage : IPage<IBar>
{
    public MockBarPage(IEnumerable<IBar> items)
    {
        Items = items;
    }

    public IEnumerable<IBar> Items { get; }
    public string? NextPageToken => null;
}
